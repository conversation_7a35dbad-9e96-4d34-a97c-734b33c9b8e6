<template>
  <el-dialog
    v-model="dialogVisible"
    title="Edit Key Model"
    width="90%"
    :before-close="handleClose"
    class="edit-keymodel-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-position="top"
      class="keymodel-form"
    >
      <!-- Basic Information Section -->
      <el-card class="basic-info-card" shadow="never">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="Colorway ID" prop="colorwayId">
              <el-select-v2
                v-model="form.colorwayId"
                :options="colorwayOptions"
                placeholder="Enter Colorway ID"
                clearable
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Product Name" prop="productName">
              <el-input
                v-model="form.productName"
                placeholder="Enter Product Name"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="ISD" prop="isd">
              <el-input v-model="form.isd" placeholder="Enter ISD" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="Material ID" prop="materialId">
              <el-select-v2
                v-model="form.materialId"
                :options="materialOptions"
                placeholder="Enter Material ID"
                clearable
                filterable
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="Factory" prop="fty">
              <el-select-v2
                v-model="form.fty"
                :options="factoryOptions"
                placeholder="Enter Factory"
                clearable
                filterable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Season" prop="season">
              <el-select-v2
                v-model="form.season"
                :options="seasonOptions"
                placeholder="Enter Season"
                clearable
                filterable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="Key Model Type" prop="keyModelType">
              <el-select
                v-model="form.keyModelType"
                placeholder="Select Key Model Type"
                style="width: 100%"
                clearable
              >
                <el-option label="HR material" value="HR material" />
                <el-option label="Express calendar" value="Express calendar" />
                <el-option label="Incubate models" value="Incubate models" />
                <el-option label="Hybrid & Next" value="Hybrid & Next" />
                <el-option
                  label="Design/ Construction complexity"
                  value="Design/ Construction complexity"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Quantity" prop="quantity">
              <el-input-number
                v-model="form.quantity"
                :min="0"
                placeholder="Enter Quantity"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="PO Received Date" prop="poReceivedDate">
              <el-date-picker
                v-model="form.poReceivedDate"
                type="date"
                placeholder="Enter PO Received Date"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- Stage Information -->
      <el-row :gutter="24" style="margin-top: 24px">
        <!-- Product Creation Section -->
        <el-col :span="8">
          <div class="form-section">
            <h3 class="section-title product">PRODUCT CREATION</h3>

            <div class="subsection">
              <h4 class="subsection-title">GTMF RFC UPDATE</h4>
              <el-form-item label="Update Date" prop="updateDate">
                <el-date-picker
                  v-model="form.updateDate"
                  type="date"
                  placeholder="Enter Update Date"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item label="Checks">
                <el-checkbox v-model="form.poReceived">PO Received</el-checkbox>
                <el-checkbox v-model="form.pfcValidated"
                  >PFC Validated</el-checkbox
                >
                <el-checkbox v-model="form.ptrssCfmed"
                  >PTRSS CFM'ed</el-checkbox
                >
              </el-form-item>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">DESIGN KEY FEATURE</h4>
              <el-form-item prop="designKeyFeature">
                <el-input
                  v-model="form.designKeyFeature"
                  type="textarea"
                  :rows="3"
                  placeholder="Enter Design Key Feature"
                />
              </el-form-item>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">DEV LEARNING</h4>
              <el-form-item prop="devLearning">
                <el-input
                  v-model="form.devLearning"
                  type="textarea"
                  :rows="3"
                  placeholder="Enter Dev Learning"
                />
              </el-form-item>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">HR MATERIAL UPDATE</h4>
              <el-form-item prop="hrMaterialUpdate">
                <el-input
                  v-model="form.hrMaterialUpdate"
                  placeholder="Enter HR material update"
                />
              </el-form-item>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">GTM COMMENT</h4>
              <el-form-item prop="gtmComment">
                <el-input
                  v-model="form.gtmComment"
                  type="textarea"
                  :rows="3"
                  placeholder="Enter GTM comment"
                />
              </el-form-item>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Cancel</el-button>
        <!-- <el-button type="primary" @click="handleSubmit" :loading="loading">
          Update
        </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { useKeyModelStore } from "@/stores/keymodel";
import type { KeyModelRecord } from "@/api/keymodel";
import { useFilter } from "@/composables/useFilter";

interface Props {
  visible: boolean;
  record: KeyModelRecord | null;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "submit", data: any): void;
  (e: "success", data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Store
const keyModelStore = useKeyModelStore();

// Filter composable for distinct values
const { distinctValues, loadingDistinctValues, fetchDistinctValues } =
  useFilter();

// Refs
const formRef = ref<FormInstance>();
const loading = ref(false);
const dialogVisible = ref(false);

// Computed
const seasons = computed(() => keyModelStore.seasons);
const factories = computed(() => keyModelStore.factories);
const keyModelTypes = computed(() => keyModelStore.keyModelTypes);

// Options for el-select-v2
const colorwayOptions = computed(() =>
  distinctValues.value.colorways.map((item) => ({ label: item, value: item })),
);

const materialOptions = computed(() =>
  distinctValues.value.materials.map((item) => ({ label: item, value: item })),
);

const factoryOptions = computed(() =>
  distinctValues.value.factories.map((item) => ({ label: item, value: item })),
);

const seasonOptions = computed(() =>
  distinctValues.value.seasons.map((item) => ({ label: item, value: item })),
);

// Remote query functions for el-select-v2
const queryColorways = (query: string) => {
  if (query) {
    return distinctValues.value.colorways
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.colorways.map((item) => ({
    label: item,
    value: item,
  }));
};

const queryMaterials = (query: string) => {
  if (query) {
    return distinctValues.value.materials
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.materials.map((item) => ({
    label: item,
    value: item,
  }));
};

const queryFactories = (query: string) => {
  if (query) {
    return distinctValues.value.factories
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.factories.map((item) => ({
    label: item,
    value: item,
  }));
};

const querySeasons = (query: string) => {
  if (query) {
    return distinctValues.value.seasons
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.seasons.map((item) => ({
    label: item,
    value: item,
  }));
};

// Form data structure
const getEmptyForm = () => ({
  // Basic info
  colorwayId: "",
  materialId: "",
  keyModelType: "",
  productName: "",
  fty: "",
  quantity: 0,
  isd: "",
  season: "",
  poReceivedDate: "",

  // Product Creation Stage
  updateDate: "",
  poReceived: false,
  pfcValidated: false,
  ptrssCfmed: false,
  designKeyFeature: "",
  devLearning: "",
  hrMaterialUpdate: "",
  gtmComment: "",

  // Commercial Stage - EST
  estDate: "",
  firstEstPass: false,
  secondEstPass: false,
  fittingApproval: false,
  ptrssTestedEst: false,
  estComment: "",

  // Commercial Stage - FST
  sampleReceivedDate: "",
  fstDate: "",
  firstFstPass: false,
  secondFstPass: false,
  ptrssTestedFst: false,
  pfcCfmed: false,
  toolingCfmed: false,
  fstComment: "",

  // Manufacturing Stage - PROD MATERIAL
  prodMaterialUpdateDate: "",
  physicalTestPass: false,
  visualCheckPass: false,
  prodMaterialComment: "",

  // Manufacturing Stage - MPPA Process
  processAssessmentDate: "",
  mppaCheckProcess: false,
  mppaPassProcess: false,
  processComment: "",

  // Manufacturing Stage - MPPA Product
  productAssessmentDate: "",
  mppaCheckProduct: false,
  mppaPassProduct: false,
  productComment: "",
});

const form = reactive(getEmptyForm());

// Form validation rules
const formRules: FormRules = {
  colorwayId: [
    { required: true, message: "Please enter Colorway ID", trigger: "blur" },
  ],
  materialId: [
    { required: true, message: "Please enter Material ID", trigger: "blur" },
  ],
  keyModelType: [
    {
      required: true,
      message: "Please select Key Model Type",
      trigger: "change",
    },
  ],
  productName: [
    { required: true, message: "Please enter Product Name", trigger: "blur" },
  ],
  fty: [
    { required: true, message: "Please select Factory", trigger: "change" },
  ],
  season: [
    { required: true, message: "Please select Season", trigger: "change" },
  ],
  isd: [{ required: true, message: "Please enter ISD", trigger: "blur" }],
};

// Watch for visibility changes
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.record) {
      // Load record data into form
      loadRecordToForm(props.record);
    }
  },
);

watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// Helper function to format dates for form
const formatDateForForm = (date: string | null) => {
  if (!date) return null;
  try {
    return new Date(date).toISOString().split("T")[0];
  } catch {
    return null;
  }
};

// Helper function to format dates for API
const formatDateForAPI = (date: string | null) => {
  if (!date) return null;
  // Convert YYYY-MM-DD to ISO 8601 format
  return new Date(date + "T00:00:00.000Z").toISOString();
};

// Load record data into form
const loadRecordToForm = (record: KeyModelRecord) => {
  const formData = { ...record };

  // Format dates for form display
  const dateFields = [
    "poReceivedDate",
    "updateDate",
    "estDate",
    "sampleReceivedDate",
    "fstDate",
    "prodMaterialUpdateDate",
    "processAssessmentDate",
    "productAssessmentDate",
  ];

  dateFields.forEach((field) => {
    (formData as any)[field] = formatDateForForm((record as any)[field]);
  });

  Object.assign(form, formData);
};

// Clean form data for API
const cleanFormData = (formData: any) => {
  const cleaned = { ...formData };

  // Convert date fields to proper format or remove if empty
  const dateFields = [
    "poReceivedDate",
    "updateDate",
    "estDate",
    "sampleReceivedDate",
    "fstDate",
    "prodMaterialUpdateDate",
    "processAssessmentDate",
    "productAssessmentDate",
  ];

  dateFields.forEach((field) => {
    if (
      cleaned[field] === "" ||
      cleaned[field] === null ||
      cleaned[field] === undefined
    ) {
      delete cleaned[field];
    } else if (typeof cleaned[field] === "string") {
      cleaned[field] = formatDateForAPI(cleaned[field]);
    }
  });

  // Remove empty string fields that should be null
  const optionalFields = [
    "designKeyFeature",
    "devLearning",
    "hrMaterialUpdate",
    "gtmComment",
    "fstComment",
    "prodMaterialComment",
    "processComment",
    "productComment",
  ];

  optionalFields.forEach((field) => {
    if (cleaned[field] === "") {
      delete cleaned[field];
    }
  });

  return cleaned;
};

// Methods
const handleClose = () => {
  dialogVisible.value = false;
  // Reset form
  Object.assign(form, getEmptyForm());
  formRef.value?.clearValidate();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    // Lấy UserID từ localStorage
    const userDataStr = localStorage.getItem("user-data");
    const userData = userDataStr ? JSON.parse(userDataStr) : null;

    // Thêm UserID và updatedBy vào form data
    const updateData = {
      ...form, // Bỏ .value vì form là reactive, không phải ref
      userId: userData?.userID || "", // Giữ nguyên string
      updatedBy: userData?.userName || "Unknown",
    };

    console.log("Update data:", updateData); // Debug log

    if (!props.record?.id) {
      console.error("Record ID is missing");
      return;
    }

    const result = await keyModelStore.updateRecord(
      props.record.id,
      updateData,
    );
    if (result) {
      emit("success", result);
      handleClose();
    }
  } catch (error) {
    console.error("Update error:", error);
  }
};

// Lifecycle
onMounted(async () => {
  // Fetch initial distinct values for select options
  await fetchDistinctValues();
});
</script>

<style scoped>
.edit-keymodel-dialog :deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.keymodel-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 10px;
  overflow-x: hidden;
}

.basic-info-card {
  margin-bottom: 0;
}

.basic-info-card :deep(.el-card__header) {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.basic-info-card h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-section {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;

  padding: 16px;
  height: 100%;
  background: var(--el-bg-color-page);
}

.section-title {
  margin: 0 0 16px 0;
  padding: 8px 12px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.section-title.product {
  background: #16a085;
}

.section-title.commercial {
  background: #f39c12;
}

.section-title.manufacturing {
  background: #2980b9;
}

.subsection {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.subsection:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subsection-title {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: bold;
  color: var(--el-text-color-regular);
  text-transform: uppercase;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox) {
  display: block;
  margin-bottom: 8px;
  margin-right: 0;
}

/* Dark mode support */
:deep(.dark) .form-section {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:deep(.dark) .subsection {
  border-color: var(--el-border-color);
}

:deep(.el-form-item__content) {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  font-size: var(--font-size);
  line-height: 32px;
  min-width: 0;
  position: relative;
  flex-direction: column;
}
</style>
