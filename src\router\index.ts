import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/LoginPage.vue'),
    meta: {
      requiresAuth: false,
      title: 'Login',
      layout: false
    }
  },
  {
    path: '/',
    component: () => import('@/components/layout/AppLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'key-model-tracking',
        name: 'KeyModelTracking',
        component: () => import('@/pages/KeyModelTrackingPage.vue'),
        meta: {
          title: 'Key Model Tracking'
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/pages/NotFoundPage.vue'),
    meta: { layout: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// ✅ Global navigation guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  const isAuthenticated = authStore.isAuthenticated

  if (to.meta.requiresAuth && !isAuthenticated) {
    // Chưa login mà vào route cần auth
    return next('/login')
  }

  if (to.path === '/login' && isAuthenticated) {
    // Đã login rồi mà vào /login
    return next('/key-model-tracking')
  }

  next()
})

export default router
