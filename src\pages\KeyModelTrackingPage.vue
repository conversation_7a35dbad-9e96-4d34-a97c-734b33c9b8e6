<template>
  <div class="keymodel-page">
    <!-- Filters -->
    <KeyModelFilters @search="handleSearch" @clear="handleClear" />

    <!-- Data Table -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <h3>Key Model Records</h3>
        </div>
      </template>

      <KeyModelTable
        :records="records"
        :loading="loading"
        :error="error"
        :has-active-search="hasActiveSearch"
        @row-click="handleRowClick"
        @view-details="handleViewDetails"
        @clear-error="clearError"
      />
    </el-card>

    <!-- Details Dialog -->
    <KeyModelDetailsDialog
      v-model:visible="detailsVisible"
      :record="selectedRecord"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useKeyModelStore } from "@/stores/keymodel";
import KeyModelTable from "@/components/keymodel/KeyModelTable.vue";
import KeyModelDetailsDialog from "@/components/keymodel/KeyModelDetailsDialog.vue";
import KeyModelFilters from "@/components/keymodel/KeyModelFilters.vue";
import { useFilter } from "@/composables/useFilter";
import type { KeyModelRecord, KeyModelFilterOptions } from "@/api/keymodel";

// Composables
const keyModelStore = useKeyModelStore();
const { filters, fetchDistinctValues } = useFilter();

// Local state
const detailsVisible = ref(false);
const selectedRecord = ref<KeyModelRecord | null>(null);
const hasActiveSearch = ref(false);

// Computed
const records = computed(() => {
  // If there's an active search, show filtered records (even if empty)
  if (hasActiveSearch.value) {
    return keyModelStore.filteredRecords;
  }
  // Otherwise, always show all records
  return keyModelStore.records;
});
const loading = computed(() => keyModelStore.isLoading);
const error = computed(() => keyModelStore.error);

// Methods
const handleRowClick = (record: KeyModelRecord): void => {
  selectedRecord.value = record;
  detailsVisible.value = true;
};

const handleViewDetails = (record: KeyModelRecord): void => {
  selectedRecord.value = record;
  detailsVisible.value = true;
};

const clearError = (): void => {
  keyModelStore.clearError();
};

// Filter methods
const handleSearch = async (filters: KeyModelFilterOptions): Promise<void> => {
  // Check if there are any active filters
  const hasFilters =
    Object.keys(filters).length > 0 &&
    Object.values(filters).some(
      (value) =>
        value !== undefined &&
        value !== null &&
        value !== "" &&
        value.toString().trim() !== ""
    );

  // Set flag to indicate active search only if there are actual filters
  hasActiveSearch.value = hasFilters;

  // Filter records locally instead of calling API
  const allRecords = keyModelStore.records;
  const filteredRecords = allRecords.filter((record) => {
    // Colorway ID filter
    if (
      filters.colorwayId &&
      !record.colorwayId
        .toLowerCase()
        .includes(filters.colorwayId.toLowerCase())
    ) {
      return false;
    }

    // Material ID filter
    if (
      filters.materialId &&
      !record.materialId
        .toLowerCase()
        .includes(filters.materialId.toLowerCase())
    ) {
      return false;
    }

    // Key Model Type filter
    if (filters.keyModelType && record.keyModelType !== filters.keyModelType) {
      return false;
    }

    // Product Name filter
    if (
      filters.productName &&
      !record.productName
        .toLowerCase()
        .includes(filters.productName.toLowerCase())
    ) {
      return false;
    }

    // ISD filter
    if (
      filters.isd &&
      !record.isd.toLowerCase().includes(filters.isd.toLowerCase())
    ) {
      return false;
    }

    // Factory filter
    if (filters.fty && record.fty !== filters.fty) {
      return false;
    }

    // Season filter
    if (filters.season && record.season !== filters.season) {
      return false;
    }

    // Status filter
    if (
      filters.isActive !== undefined &&
      record.isActive !== filters.isActive
    ) {
      return false;
    }

    return true;
  });

  // Update store with filtered records
  keyModelStore.setFilteredRecords(filteredRecords);
};

const handleClear = async (): Promise<void> => {
  // Reset search flag
  hasActiveSearch.value = false;
  // Reset to original records
  keyModelStore.clearFilters();
};

// Lifecycle
onMounted(async () => {
  await keyModelStore.fetchRecords();
});
</script>

<style scoped>
.keymodel-page {
  padding: 12px;
}

/* Filters Card */
.filters-card {
  margin-bottom: 24px;
  border-radius: 4px;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filters-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.filters-actions {
  display: flex;
  gap: 12px;
}

/* Table Card */
.table-card {
  border-radius: 4px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.record-count {
  font-size: 14px;
}

/* Details Dialog */
.record-details {
  padding: 0;
}

.details-section {
  margin-bottom: 32px;
}

.details-section:last-child {
  margin-bottom: 0;
}

.details-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.detail-item label {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #909399;
}

.detail-item span {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* Responsive Design */
@media (max-width: 768px) {
  .keymodel-page {
    padding: 16px;
  }

  .filters-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
