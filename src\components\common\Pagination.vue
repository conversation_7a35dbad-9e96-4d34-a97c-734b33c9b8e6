<template>
  <div class="pagination-container">
    <!-- Page Info -->
    <div class="page-info">
      <span class="page-text">
        Showing {{ pageInfo.start }} to {{ pageInfo.end }} of
        {{ pageInfo.total }} records
      </span>
    </div>

    <!-- Pagination Controls -->
    <div class="pagination-controls">
      <!-- Page Size Selector -->
      <div class="page-size-selector">
        <span class="page-size-label">Show:</span>
        <el-select
          :model-value="pageSize"
          @update:model-value="handlePageSizeChange"
          size="small"
          style="width: 80px"
        >
          <el-option label="10" :value="10" />
          <el-option label="20" :value="20" />
          <el-option label="50" :value="50" />
          <el-option label="100" :value="100" />
        </el-select>
        <span class="page-size-text">per page</span>
      </div>

      <!-- Navigation Buttons -->
      <div class="navigation-buttons">
        <!-- First Page -->
        <el-button :disabled="!hasPrevPage" @click="goToFirstPage" size="small">
          <el-icon><DArrowLeft /></el-icon>
        </el-button>

        <!-- Previous Page -->
        <el-button :disabled="!hasPrevPage" @click="prevPage" size="small">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <el-button
            v-for="page in pageNumbers"
            :key="page"
            :type="page === currentPage ? 'primary' : 'default'"
            :disabled="page === '...'"
            @click="page !== '...' ? setPage(page as number) : null"
            size="small"
            class="page-number-btn"
          >
            {{ page }}
          </el-button>
        </div>

        <!-- Next Page -->
        <el-button :disabled="!hasNextPage" @click="nextPage" size="small">
          <el-icon><ArrowRight /></el-icon>
        </el-button>

        <!-- Last Page -->
        <el-button :disabled="!hasNextPage" @click="goToLastPage" size="small">
          <el-icon><DArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  ArrowLeft,
  ArrowRight,
  DArrowLeft,
  DArrowRight,
} from "@element-plus/icons-vue";

interface PageInfo {
  total: number;
  start: number;
  end: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

interface Props {
  pageInfo: PageInfo;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  totalPages: number;
}

interface Emits {
  (e: "page-change", page: number): void;
  (e: "page-size-change", size: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Computed
const pageNumbers = computed(() => {
  const pages: (number | string)[] = [];
  const total = props.totalPages;
  const current = props.currentPage;
  const maxVisible = 5;

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (current > 3) {
      pages.push("...");
    }

    // Show pages around current page
    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) {
      if (i !== 1 && i !== total) {
        pages.push(i);
      }
    }

    if (current < total - 2) {
      pages.push("...");
    }

    // Always show last page
    if (total > 1) {
      pages.push(total);
    }
  }

  return pages;
});

// Methods
const setPage = (page: number) => {
  emit("page-change", page);
};

const prevPage = () => {
  if (props.hasPrevPage) {
    emit("page-change", props.currentPage - 1);
  }
};

const nextPage = () => {
  if (props.hasNextPage) {
    emit("page-change", props.currentPage + 1);
  }
};

const goToFirstPage = () => {
  emit("page-change", 1);
};

const goToLastPage = () => {
  emit("page-change", props.totalPages);
};

const handlePageSizeChange = (size: number) => {
  emit("page-size-change", size);
};
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

.page-info {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.page-text {
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.page-size-label {
  font-weight: 500;
}

.page-size-text {
  font-weight: 500;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-number-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
}

.page-number-btn:disabled {
  cursor: default;
  opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .page-size-selector {
    order: 2;
  }

  .navigation-buttons {
    order: 1;
  }
}

@media (max-width: 480px) {
  .navigation-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-numbers {
    order: 2;
    width: 100%;
    justify-content: center;
    margin-top: 8px;
  }
}
</style>
