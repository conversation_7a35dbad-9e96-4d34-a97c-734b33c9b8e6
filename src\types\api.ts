import type { OrderStatus, ProductStatus, MovementType } from './global'

// Base API Types
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// Shipment Tracking Types
export interface ShipmentTracking {
  date: string
  khpo: string
  style_no: string
  color_code: string
  ship_id: string
  status: string
  qty: number
  rm01: number
  rm03: number
  wip31: number
  wip33: number
  wip6: number
  wip8: number
  fg10: number
  fg14: number
}

export interface ShipmentTrackingResponse {
  code: number
  data: ShipmentTracking[]
  message: string
}

// Auth Types
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface User {
  id: number
  username: string
  email: string
  role: string
  Token: string
  [key: string]: unknown
}

// Order Types
export interface Order {
  id: number
  orderCode: string
  orderDate: string
  planDeliveryDate: string
  actualDeliveryDate?: string
  orderStatus: OrderStatus
  items: OrderItem[]
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  orderDetailId: number
  productId: number
  productQuantity: number
  orderPartStatus: OrderStatus
  shippedQuantity?: number
}

export interface CreateOrderRequest {
  orderDate: string
  planDeliveryDate: string
  actualDeliveryDate?: string
  orderStatus: OrderStatus
  items: CreateOrderItem[]
}

export interface CreateOrderItem {
  productId: number
  productQuantity: number
  orderPartStatus: OrderStatus
}

export interface UpdateOrderRequest {
  orderDate: string
  planDeliveryDate: string
  actualDeliveryDate?: string
  orderStatus: OrderStatus
  newItems: CreateOrderItem[]
  updatedItems: OrderItem[]
  deletedItemIds: { productId: number }[]
}

// Product Types
export interface Product {
  id: number
  productCode: string
  productName: string
  description?: string
  category?: string
  unit?: string
  price?: number
  cost?: number
  weight?: number
  injectionTime?: number
  status: ProductStatus
  createdAt: string
  updatedAt: string
}

export interface CreateProductRequest {
  productCode: string
  productName: string
  description?: string
  category?: string
  unit?: string
  price?: number
  cost?: number
  weight?: number
  injectionTime?: number
  status: ProductStatus
}

export interface UpdateProductRequest {
  productCode?: string
  productName?: string
  description?: string
  category?: string
  unit?: string
  price?: number
  cost?: number
  weight?: number
  injectionTime?: number
  status?: ProductStatus
}

// Material Types
export interface Material {
  id: number
  materialCode: string
  materialName: string
  description?: string
  unit?: string
  cost?: number
  supplier?: string
  status: ProductStatus
  createdAt: string
  updatedAt: string
}

export interface CreateMaterialRequest {
  materialCode: string
  materialName: string
  description?: string
  unit?: string
  cost?: number
  supplier?: string
  status: ProductStatus
}

export interface UpdateMaterialRequest {
  materialCode?: string
  materialName?: string
  description?: string
  unit?: string
  cost?: number
  supplier?: string
  status?: ProductStatus
}

// BOM Types
export interface BOM {
  id: number
  bomCode: string
  bomName: string
  productId: number
  product: Product
  items: BOMItem[]
  createdAt: string
  updatedAt: string
}

export interface BOMItem {
  id: number
  materialId: number
  material: Material
  quantity: number
  unit: string
}

export interface CreateBOMRequest {
  bomCode: string
  bomName: string
  productId: number
  items: CreateBOMItem[]
}

export interface CreateBOMItem {
  materialId: number
  quantity: number
  unit: string
}

export interface UpdateBOMRequest {
  bomCode?: string
  bomName?: string
  productId?: number
  items?: CreateBOMItem[]
}

// Movement Types
export interface Movement {
  id: number
  movementCode: string
  movementType: MovementType
  movementDate: string
  productId: number
  product: Product
  quantity: number
  unit: string
  unitPrice?: number
  totalAmount?: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateMovementRequest {
  movementType: MovementType
  movementDate: string
  productId: number
  quantity: number
  unit: string
  unitPrice?: number
  notes?: string
}

export interface UpdateMovementRequest {
  movementType?: MovementType
  movementDate?: string
  productId?: number
  quantity?: number
  unit?: string
  unitPrice?: number
  notes?: string
}

// Inventory Types
export interface Inventory {
  id: number
  productId: number
  product: Product
  currentStock: number
  unit: string
  lastUpdated: string
}

export interface InventoryMovement {
  id: number
  productId: number
  product: Product
  movementType: MovementType
  quantity: number
  unit: string
  date: string
}

// Filter and Search Types
export interface FilterOptions {
  status?: string
  category?: string
  dateFrom?: string
  dateTo?: string
  search?: string
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface PaginationOptions {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Dashboard Types
export interface DashboardStats {
  totalOrders: number
  totalProducts: number
  totalMaterials: number
  pendingOrders: number
  lowStockAlerts: number
  totalInventoryValue: number
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}

export interface OrderSummary {
  month: string
  totalOrders: number
  totalValue: number
}

export interface MovementSummary {
  movementType: MovementType
  count: number
  totalValue: number
} 
