// API Response types
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// Common types
export interface BaseEntity {
  id: number
  createdAt?: string
  updatedAt?: string
}

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}

export interface FilterOption {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'notIn'
  value: any
}

export interface SortOption {
  field: string
  direction: 'asc' | 'desc'
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'date' | 'datetime' | 'textarea' | 'checkbox' | 'radio'
  required?: boolean
  placeholder?: string
  options?: SelectOption[]
  validation?: {
    required?: boolean
    min?: number
    max?: number
    pattern?: RegExp
    message?: string
  }
}

// Status types
export type OrderStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
export type ProductStatus = 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED'
export type MovementType = 'STOCK_IN' | 'STOCK_OUT' | 'STOCK_ADJUST'

// Event types
export interface AppEvent {
  type: string
  payload?: any
  timestamp: number
}

// Theme types
export type Theme = 'light' | 'dark' | 'auto'
export type Language = 'en' | 'vi' | 'zh'

// Permission types
export interface Permission {
  resource: string
  action: 'create' | 'read' | 'update' | 'delete'
}

export interface Role {
  id: number
  name: string
  permissions: Permission[]
}

// User types
export interface UserProfile {
  id: number
  username: string
  email: string
  fullName?: string
  avatar?: string
  role: Role
  permissions: Permission[]
  preferences: {
    theme: Theme
    language: Language
    timezone: string
  }
}

// Notification types
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: number
  read: boolean
  action?: {
    label: string
    url: string
  }
}

// Chart types
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}

export interface ChartOptions {
  responsive: boolean
  maintainAspectRatio: boolean
  plugins?: {
    legend?: {
      display: boolean
      position: 'top' | 'bottom' | 'left' | 'right'
    }
    tooltip?: {
      enabled: boolean
    }
  }
  scales?: {
    y?: {
      beginAtZero: boolean
    }
  }
} 