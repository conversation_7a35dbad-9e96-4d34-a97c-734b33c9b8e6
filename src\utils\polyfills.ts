// Polyfills for Node.js modules in browser environment
import { <PERSON><PERSON><PERSON> } from 'buffer'

// Make Buffer available globally
if (typeof window !== 'undefined') {
  (window as any).Buffer = Buffer
  ;(window as any).global = window
  
  // Polyfill process for xlsx-populate
  if (!(window as any).process) {
    ;(window as any).process = {
      env: {},
      version: '',
      versions: {},
      platform: 'browser',
      nextTick: (fn: any) => Promise.resolve().then(fn),
      browser: true
    }
  }
  
  // Polyfill for crypto if needed
  if (!window.crypto) {
    ;(window as any).crypto = {
      getRandomValues: (arr: any) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256)
        }
        return arr
      }
    }
  }
  
  // Polyfill for stream if needed (for xlsx-populate)
  if (!(window as any).stream) {
    ;(window as any).stream = {
      Readable: class Readable {
        constructor() {}
      },
      Writable: class Writable {
        constructor() {}
      }
    }
  }
  
  // Polyfill for util if needed
  if (!(window as any).util) {
    ;(window as any).util = {
      inherits: () => {},
      format: (str: string, ...args: any[]) => {
        return str.replace(/%s/g, () => args.shift() || '')
      }
    }
  }
  
  // Polyfill for events if needed
  if (!(window as any).events) {
    ;(window as any).events = {
      EventEmitter: class EventEmitter {
        constructor() {}
        on() {}
        emit() {}
      }
    }
  }
  
  // Polyfill for path if needed
  if (!(window as any).path) {
    ;(window as any).path = {
      join: (...args: string[]) => args.join('/'),
      resolve: (...args: string[]) => args.join('/'),
      dirname: (path: string) => path.split('/').slice(0, -1).join('/'),
      basename: (path: string) => path.split('/').pop() || ''
    }
  }
  
  // Polyfill for fs if needed (minimal implementation)
  if (!(window as any).fs) {
    ;(window as any).fs = {
      readFileSync: () => { throw new Error('fs.readFileSync not available in browser') },
      writeFileSync: () => { throw new Error('fs.writeFileSync not available in browser') },
      existsSync: () => false
    }
  }
}

export { Buffer } 