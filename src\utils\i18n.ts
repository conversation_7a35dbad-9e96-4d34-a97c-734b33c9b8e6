import { useI18n } from 'vue-i18n'
import { ref } from 'vue'

// Available languages
export const availableLocales = [
  { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
]

// Current locale reactive reference
export const currentLocale = ref('vi')

// Language switching function
export function useLanguageSwitcher() {
  const { locale } = useI18n()
  
  function switchLanguage(langCode: string) {
    locale.value = langCode
    currentLocale.value = langCode
    // Store in localStorage for persistence
    localStorage.setItem('locale', langCode)
  }
  
  function getCurrentLocale() {
    return currentLocale.value
  }
  
  function initializeLocale() {
    // Get stored locale or browser locale
    const storedLocale = localStorage.getItem('locale')
    if (storedLocale && availableLocales.some(lang => lang.code === storedLocale)) {
      switchLanguage(storedLocale)
    } else {
      // Use browser language detection
      const browserLang = navigator.language.toLowerCase()
      if (browserLang.startsWith('zh')) {
        switchLanguage('zh')
      } else {
        switchLanguage('vi') // Default to Vietnamese
      }
    }
  }
  
  return {
    switchLanguage,
    getCurrentLocale,
    initializeLocale,
    availableLocales
  }
}

// Translation helper with fallback
export function useTranslation() {
  const { t, te } = useI18n()
  
  function translate(key: string, fallback?: string) {
    if (te(key)) {
      return t(key)
    }
    return fallback || key
  }
  
  return {
    t: translate,
    te
  }
} 