<template>
  <div class="keymodel-table-root">
    <div class="table-header">
      <div class="table-actions">
        <el-button type="primary" @click="handleAdd">Add</el-button>
      </div>
      <div class="table-info">
        <span class="record-count">
          Total: {{ records?.length || 0 }} records
        </span>
        <span
          v-if="hasActiveSearch && records?.length === 0"
          class="no-results-message"
        >
          (No results found for your search criteria)
        </span>
      </div>
    </div>

    <!-- Data Table Component -->
    <KeyModelDataTable
      :records="paginatedRecords"
      :loading="loading"
      :show-status="true"
      :has-active-search="hasActiveSearch"
      @edit="handleEdit"
      @export-single="handleExportSingle"
      @delete="handleDelete"
      @create-product-dialog="handleCreateProductDialog"
      @commercial-stage-dialog="handleCommercialStageDialog"
      @manufacturing-stage-dialog="handleManufacturingStageDialog"
    />
    <!-- Pagination -->
    <div style="margin-top: 20px; display: flex; justify-content: center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalRecords.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-message">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="true"
        @close="$emit('clear-error')"
      />
    </div>

    <!-- Add Dialog -->
    <AddKeyModelDialog
      v-model:visible="addDialogVisible"
      @submit="handleAddSubmit"
    />

    <!-- Edit Dialog -->
    <EditKeyModelDialog
      v-model:visible="editDialogVisible"
      :record="editingRecord"
      @submit="handleEditSubmit"
    />
    <!-- Create Product Dialog -->
    <CreateProductDialog
      v-model:visible="handleCreateProductDialogVisible"
      :record="editingRecord"
    />
    <!-- Commercial Stage Dialog -->
    <CommercialStageDialog
      v-model:visible="handleCommercialStageDialogVisible"
      :record="editingRecord"
    />
    <!-- Manufacturing Stage Dialog -->
    <ManufacturingStageDialog
      v-model:visible="handleManufacturingStageDialogVisible"
      :record="editingRecord"
    />
    <!-- Delete Confirmation Dialog -->
    <!-- <el-dialog
      title="Confirm Delete"
      v-model="deleteDialogVisible"
      width="400px"
    >
      <span>Are you sure you want to delete this record?</span>
      <template #footer>
        <el-button @click="cancelDelete">Canel</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleting">
          Delete
        </el-button>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { useKeyModelStore } from "@/stores/keymodel";
import { useKeyModelExport } from "@/composables/useKeyModelExport";
import { useKeyModelPagination } from "@/composables/useKeyModelPagination";
import KeyModelDataTable from "./KeyModelDataTable.vue";
import AddKeyModelDialog from "./AddKeyModelDialog.vue";
import EditKeyModelDialog from "./EditKeyModelDialog.vue";
import CreateProductDialog from "./CreateProductDialog.vue";
import CommercialStageDialog from "./CommercialStageDialog.vue";
import ManufacturingStageDialog from "./ManufacturingStageDialog.vue";
import type { KeyModelRecord } from "@/api/keymodel";

// Props & Emits (same as before)
interface Props {
  records: KeyModelRecord[];
  loading?: boolean;
  error?: string | null;
  hasActiveSearch?: boolean;
}

interface Emits {
  (e: "row-click", record: KeyModelRecord): void;
  (e: "view-details", record: KeyModelRecord): void;
  (e: "clear-error"): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  hasActiveSearch: false,
});

const emit = defineEmits<Emits>();

// Store & Composables
const keyModelStore = useKeyModelStore();
const { exportSingleRecord } = useKeyModelExport();
const {
  currentPage,
  pageSize,
  totalPages,
  hasNextPage,
  hasPrevPage,
  paginatedRecords,
  pageInfo,
  totalRecords,
  setRecords,
  setPage,
  setPageSize,
} = useKeyModelPagination();

// Local state
const deleteDialogVisible = ref(false);
const deleteRecord = ref<KeyModelRecord | null>(null);
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const handleCreateProductDialogVisible = ref(false);
const handleCommercialStageDialogVisible = ref(false);
const handleManufacturingStageDialogVisible = ref(false);
const editingRecord = ref<KeyModelRecord | null>(null);
const deleting = ref(false);

// Computed
const hasActiveSearch = computed(() => props.hasActiveSearch);

// Watch for records changes to update pagination
watch(
  () => props.records,
  (newRecords) => {
    setRecords(newRecords);
  },
  { immediate: true },
);

// Event handlers

const handleAdd = () => {
  addDialogVisible.value = true;
};

const handleAddSubmit = async (data: any) => {
  const result = await keyModelStore.createRecord(data);
  if (result) {
    addDialogVisible.value = false;
  }
};

const handleEdit = (record: KeyModelRecord, index: number) => {
  editingRecord.value = record;
  editDialogVisible.value = true;
};

const handleCreateProductDialog = (record: KeyModelRecord, index: number) => {
  editingRecord.value = record;
  handleCreateProductDialogVisible.value = true;
};

const handleCommercialStageDialog = (record: KeyModelRecord, index: number) => {
  editingRecord.value = record;
  handleCommercialStageDialogVisible.value = true;
};
const handleManufacturingStageDialog = (
  record: KeyModelRecord,
  index: number,
) => {
  editingRecord.value = record;
  handleManufacturingStageDialogVisible.value = true;
};
const handleEditSubmit = async (payload: { id: string; data: any }) => {
  const result = await keyModelStore.updateRecord(payload.id, payload.data);
  if (result) {
    editDialogVisible.value = false;
    editingRecord.value = null;
  }
};

const handleExportSingle = async (record: KeyModelRecord) => {
  await exportSingleRecord(record);
};

const handleDelete = (record: KeyModelRecord, index: number) => {
  deleteRecord.value = record;
  deleteDialogVisible.value = true;
};

const confirmDelete = async () => {
  if (!deleteRecord.value?.id) return;

  deleting.value = true;
  const success = await keyModelStore.deleteRecord(deleteRecord.value.id);

  if (success) {
    deleteDialogVisible.value = false;
    // Reload data after successful delete
    await keyModelStore.fetchRecords();
  }
  deleting.value = false;
};

const cancelDelete = () => {
  deleteDialogVisible.value = false;
  deleteRecord.value = null;
};

// Pagination handlers
const handlePageChange = (page: number) => {
  setPage(page);
};

const handlePageSizeChange = (size: number) => {
  setPageSize(size);
};
</script>

<style scoped>
.keymodel-table-root {
  padding: 16px 0;
}

.table-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.error-message {
  margin-bottom: 16px;
}

.table-info {
  color: var(--el-text-color-regular);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-count {
  font-weight: 500;
}

.no-results-message {
  color: var(--el-color-warning);
  font-size: 12px;
  font-style: italic;
}
</style>
