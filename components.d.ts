/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddKeyModelDialog: typeof import('./src/components/keymodel/AddKeyModelDialog.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    CommercialStageDialog: typeof import('./src/components/keymodel/CommercialStageDialog.vue')['default']
    CreateProductDialog: typeof import('./src/components/keymodel/CreateProductDialog.vue')['default']
    CustomHeader: typeof import('./src/components/layout/CustomHeader.vue')['default']
    CustomMain: typeof import('./src/components/layout/CustomMain.vue')['default']
    CustomSidebar: typeof import('./src/components/layout/CustomSidebar.vue')['default']
    EditKeyModelDialog: typeof import('./src/components/keymodel/EditKeyModelDialog.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    KeyModelDataTable: typeof import('./src/components/keymodel/KeyModelDataTable.vue')['default']
    KeyModelDetailsDialog: typeof import('./src/components/keymodel/KeyModelDetailsDialog.vue')['default']
    KeyModelFilters: typeof import('./src/components/keymodel/KeyModelFilters.vue')['default']
    KeyModelTable: typeof import('./src/components/keymodel/KeyModelTable.vue')['default']
    LanguageSwitcher: typeof import('./src/components/common/LanguageSwitcher.vue')['default']
    ManufacturingStageDialog: typeof import('./src/components/keymodel/ManufacturingStageDialog.vue')['default']
    Pagination: typeof import('./src/components/common/Pagination.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SkeletonLoader: typeof import('./src/components/common/SkeletonLoader.vue')['default']
  }
}
