<template>
  <el-container class="app-container">
    <!-- Hide sidebar and header for 404 page -->
    <template v-if="!isNotFoundPage">
      <CustomSidebar />
      <el-container class="main-container">
        <CustomHeader />
        <CustomMain>
          <router-view v-slot="{ Component, route }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </CustomMain>
      </el-container>
    </template>
    
    <!-- Full screen for 404 page -->
    <template v-else>
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </template>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import CustomSidebar from './CustomSidebar.vue'
import CustomHeader from './CustomHeader.vue'
import CustomMain from './CustomMain.vue'

const route = useRoute()

const isNotFoundPage = computed(() => {
  return route.meta.layout === false || route.name === 'NotFound' || route.path === '/404'
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  background-color: var(--el-bg-color-page);
}

.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

/* Route transition */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 404 page styles */
.app-container:has(.not-found-page) {
  height: 100vh;
  background: transparent;
}
</style> 