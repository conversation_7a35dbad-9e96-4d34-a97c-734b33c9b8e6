<template>
  <el-aside :width="sidebarCollapsed ? '64px' : '200px'" class="sidebar">
    <div class="logo-container">
      <img :src="logoSrc" alt="Logo" class="logo" />
      <span v-show="!sidebarCollapsed" class="logo-text"
        >KEY MODEL TRACKING</span
      >
    </div>

    <el-menu
      :default-active="$route.path"
      :collapse="sidebarCollapsed"
      router
      class="sidebar-menu"
      background-color="transparent"
      text-color="var(--el-text-color-primary)"
      active-text-color="var(--el-color-primary)"
    >
      <el-menu-item index="/key-model-tracking" class="sidebar-menu-item">
        <el-icon><Document /></el-icon>
        <span> KEY MODEL TRACKING</span>
      </el-menu-item>
    </el-menu>
  </el-aside>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app";
import { Document } from "@element-plus/icons-vue";

const { t } = useI18n();
const appStore = useAppStore();
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed);
const isDarkMode = computed(() => appStore.isDarkMode);

const logoSrc = computed(() =>
  isDarkMode.value ? "/converse_white.png" : "/converse_dark.png"
);
</script>

<style scoped>
.sidebar {
  background-color: var(--el-bg-color-overlay);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s ease;
  overflow-x: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  border-bottom: 1px solid var(--el-border-color);
}

.logo {
  height: 32px;
  width: 32px;
  margin-right: 12px;
  object-fit: contain;
}

.logo-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

.sidebar-menu {
  border-right: none;
}

.el-menu-item {
  font-weight: 500;
}

.el-menu-item.is-active {
  background-color: var(--el-color-primary-light-9);
}

.sidebar-menu-item {
  font-size: 12px;
}
</style>
