// Plugins
import Components from "unplugin-vue-components/vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { resolve } from 'path'

// Utilities
import { defineConfig } from "vite";
import { fileURLToPath, URL } from "node:url";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    })
  ],
  define: { 
    "process.env": {},
    global: 'globalThis',
    // Add process polyfill for xlsx-populate
    "process": {
      env: {},
      version: '',
      versions: {},
      platform: 'browser',
      nextTick: (fn) => Promise.resolve().then(fn)
    }
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, 'src'),
    },
    extensions: [".js", ".json", ".jsx", ".mjs", ".ts", ".tsx", ".vue"],
  },
  optimizeDeps: {
    include: ['xlsx-populate', 'buffer'],
    esbuildOptions: {
      define: {
        global: 'globalThis'
      }
    }
  },
  build: {
    rollupOptions: {
      external: [],
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          xlsx: ['xlsx-populate', 'buffer']
        }
      }
    }
  },
  server: {
    host: "localhost", // 使服务器在局域网内可访问
    port: 3000,
    open: true, // 启动时自动打开浏览器
    hmr: {
      protocol: "ws", // wss => http |ws => http
      host: "localhost", // 主机
      port: 3000,
    },
    // https: {
    //   key: "certificates/key.pem",
    //   cert: "certificates/cert.pem",
    // },
    proxy: {
      "/api": {
        target: "http://localhost:8080",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
