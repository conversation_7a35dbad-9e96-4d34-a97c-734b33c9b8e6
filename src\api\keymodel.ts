
import api from './axios'
import type { ApiResponse } from '@/types/api'

// Key Model Types
export interface KeyModelRecord {
  id?: string
  colorwayId: string
  materialId: string
  keyModelType: string
  productName: string
  fty: string
  quantity: number
  isd: string
  season: string
  poReceivedDate: string
  createdDate?: string
  updatedDate?: string
  createdBy: string
  updatedBy?: string
  userId?: string  // Đổi từ number sang string
  isActive?: boolean

  // Product Creation Stage
  updateDate?: string
  poReceived?: boolean
  pfcValidated?: boolean
  ptrssCfmed?: boolean
  designKeyFeature?: string
  devLearning?: string
  hrMaterialUpdate?: string
  gtmComment?: string

  // Commercial Stage - EST UPDATE
  estDate?: string
  firstEstPass?: boolean
  secondEstPass?: boolean
  fittingApproval?: boolean
  ptrssTestedEst?: boolean
  estComment?: string

  // Commercial Stage - FST UPDATE
  sampleReceivedDate?: string
  fstDate?: string
  firstFstPass?: boolean
  secondFstPass?: boolean
  ptrssTestedFst?: boolean
  pfcCfmed?: boolean
  toolingCfmed?: boolean
  fstComment?: string

  // Manufacturing Stage - PROD MATERIAL UPDATE
  prodMaterialUpdateDate?: string
  physicalTestPass?: boolean
  visualCheckPass?: boolean
  prodMaterialComment?: string

  // Manufacturing Stage - MPPA UPDATE Process
  processAssessmentDate?: string
  mppaCheckProcess?: boolean
  mppaPassProcess?: boolean
  processComment?: string

  // Manufacturing Stage - MPPA UPDATE Product
  productAssessmentDate?: string
  mppaCheckProduct?: boolean
  mppaPassProduct?: boolean
  productComment?: string
}

export interface KeyModelFilterOptions {
  colorwayId?: string
  materialId?: string
  keyModelType?: string
  productName?: string
  isd?: string
  fty?: string
  season?: string
  isActive?: boolean
  pageNumber?: number
  pageSize?: number
}

export interface Season {
  id: number
  seasonCode: string
  seasonName: string
  isActive: boolean
}

export interface Factory {
  id: number
  factoryCode: string
  factoryName: string
  location: string
  isActive: boolean
}

export interface KeyModelType {
  id: number
  typeCode: string
  typeName: string
  description: string
  isActive: boolean
}

export interface KfxxzlRow {
  id?: number
  colorway: string
  material: string
  season: string
  factory: string
  // Add other fields as needed based on the actual database schema
  [key: string]: any
}

export interface KfxxzlFilterOptions {
  page?: number
  page_size?: number
  colorway?: string
  material?: string
  season?: string
  factory?: string
  // Add other filter fields as needed
  [key: string]: any
}

export interface KfxxzlPaginatedResponse {
  data: KfxxzlRow[]
  total: number
  page: number
  page_size: number
}

export const keyModelAPI = {
  // Key Model Records
  create: (data: Omit<KeyModelRecord, 'id' | 'createdDate' | 'updatedDate'>) =>
    api.post<ApiResponse<KeyModelRecord>>('/key-model-records', data),

  getById: (id: string) =>
    api.get<ApiResponse<KeyModelRecord>>(`/key-model-records/${id}`),

  update: (id: string, data: Partial<KeyModelRecord>) =>
    api.put<ApiResponse<KeyModelRecord>>(`/key-model-records/${id}`, data),

  delete: (id: string) =>
    api.delete<ApiResponse<null>>(`/key-model-records/${id}`),

  getAll: (filters?: KeyModelFilterOptions) => {
    const params = new URLSearchParams()
    
    if (filters?.colorwayId) params.append('colorwayId', filters.colorwayId)
    if (filters?.materialId) params.append('materialId', filters.materialId)
    if (filters?.keyModelType) params.append('keyModelType', filters.keyModelType)
    if (filters?.productName) params.append('productName', filters.productName)
    if (filters?.isd) params.append('isd', filters.isd)
    if (filters?.fty) params.append('fty', filters.fty)
    if (filters?.season) params.append('season', filters.season)
    if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString())
    
    const queryString = params.toString()
    const url = queryString ? `/key-model-records?${queryString}` : '/key-model-records'
    
    return api.get<ApiResponse<KeyModelRecord[]>>(url)
  },

  getPaginated: (filters?: KeyModelFilterOptions) => {
    const params = new URLSearchParams()
    
    if (filters?.colorwayId) params.append('colorwayId', filters.colorwayId)
    if (filters?.materialId) params.append('materialId', filters.materialId)
    if (filters?.keyModelType) params.append('keyModelType', filters.keyModelType)
    if (filters?.productName) params.append('productName', filters.productName)
    if (filters?.isd) params.append('isd', filters.isd)
    if (filters?.fty) params.append('fty', filters.fty)
    if (filters?.season) params.append('season', filters.season)
    if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString())
    if (filters?.pageNumber) params.append('pageNumber', filters.pageNumber.toString())
    if (filters?.pageSize) params.append('pageSize', filters.pageSize.toString())
    
    const queryString = params.toString()
    const url = queryString ? `/key-model-records/paginated?${queryString}` : '/key-model-records/paginated'
    
    return api.get<ApiResponse<KeyModelRecord[]>>(url)
  },

  getDistinct: (field: 'colorwayId' | 'materialId' | 'keyModelType' | 'productName' | 'isd' | 'fty' | 'season') =>
    api.get<ApiResponse<string[]>>(`/key-model-records/distinct?field=${field}`),

  // Seasons
  seasons: {
    create: (data: Omit<Season, 'id'>) =>
      api.post<ApiResponse<Season>>('/seasons', data),

    getAll: () =>
      api.get<ApiResponse<Season[]>>('/seasons'),

    getById: (id: number) =>
      api.get<ApiResponse<Season>>(`/seasons/${id}`),

    update: (id: number, data: Partial<Season>) =>
      api.put<ApiResponse<Season>>(`/seasons/${id}`, data),

    delete: (id: number) =>
      api.delete<ApiResponse<null>>(`/seasons/${id}`)
  },

  // Factories
  factories: {
    create: (data: Omit<Factory, 'id'>) =>
      api.post<ApiResponse<Factory>>('/factories', data),

    getAll: () =>
      api.get<ApiResponse<Factory[]>>('/factories'),

    getById: (id: number) =>
      api.get<ApiResponse<Factory>>(`/factories/${id}`),

    update: (id: number, data: Partial<Factory>) =>
      api.put<ApiResponse<Factory>>(`/factories/${id}`, data),

    delete: (id: number) =>
      api.delete<ApiResponse<null>>(`/factories/${id}`)
  },

  // Key Model Types
  keyModelTypes: {
    create: (data: Omit<KeyModelType, 'id'>) =>
      api.post<ApiResponse<KeyModelType>>('/key-model-types', data),

    getAll: () =>
      api.get<ApiResponse<KeyModelType[]>>('/key-model-types'),

    getById: (id: number) =>
      api.get<ApiResponse<KeyModelType>>(`/key-model-types/${id}`),

    update: (id: number, data: Partial<KeyModelType>) =>
      api.put<ApiResponse<KeyModelType>>(`/key-model-types/${id}`, data),

    delete: (id: number) =>
      api.delete<ApiResponse<null>>(`/key-model-types/${id}`)
  },

  // KFXXZL Rows API
  kfxxzlRows: {
    getFiltered: (filters?: KfxxzlFilterOptions) => {
      const params = new URLSearchParams()

      if (filters?.page) params.append('page', filters.page.toString())
      if (filters?.page_size) params.append('page_size', filters.page_size.toString())
      if (filters?.colorway) params.append('colorway', filters.colorway)
      if (filters?.material) params.append('material', filters.material)
      if (filters?.season) params.append('season', filters.season)
      if (filters?.factory) params.append('factory', filters.factory)

      // Add any additional filter parameters
      Object.keys(filters || {}).forEach(key => {
        if (!['page', 'page_size', 'colorway', 'material', 'season', 'factory'].includes(key)) {
          const value = filters?.[key]
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString())
          }
        }
      })

      const queryString = params.toString()
      const url = queryString ? `/kfxxzl-rows?${queryString}` : '/kfxxzl-rows'

      return api.get<KfxxzlPaginatedResponse>(url)
    }
  }
}

