import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

export interface ErrorInfo {
  message: string
  code?: string | number
  details?: unknown
}

export function useErrorHandler() {
  const { t } = useI18n()

  const handleError = (error: unknown, fallbackMessage?: string): void => {
    let errorMessage = fallbackMessage || t('mes.generalError')
    
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof error === 'object' && 'message' in error) {
      errorMessage = String(error.message)
    }

    ElMessage.error(errorMessage)
  }

  const handleApiError = (error: any, customMessage?: string): void => {
    const message = error.response?.data?.message || 
                   error.message || 
                   customMessage || 
                   t('mes.apiError')
    
    ElMessage.error(message)
  }

  const handleValidationError = (errors: string[]): void => {
    const message = errors.join(', ')
    ElMessage.error(message)
  }

  const handleNetworkError = (): void => {
    ElMessage.error(t('mes.networkError'))
  }

  const handleUnauthorizedError = (): void => {
    ElMessage.error(t('mes.unauthorizedError'))
  }

  const handleNotFoundError = (): void => {
    ElMessage.error(t('mes.notFoundError'))
  }

  return {
    handleError,
    handleApiError,
    handleValidationError,
    handleNetworkError,
    handleUnauthorizedError,
    handleNotFoundError
  }
} 