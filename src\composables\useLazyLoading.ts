import { ref, computed } from 'vue'

export interface LazyLoadingOptions {
  threshold?: number
  rootMargin?: string
  root?: Element | null
}

export function useLazyLoading(options: LazyLoadingOptions = {}) {
  const isVisible = ref<boolean>(false)
  const isLoading = ref<boolean>(false)
  const hasLoaded = ref<boolean>(false)

  const shouldLoad = computed(() => isVisible.value && !hasLoaded.value)

  const startLoading = (): void => {
    isLoading.value = true
  }

  const finishLoading = (): void => {
    isLoading.value = false
    hasLoaded.value = true
  }

  const reset = (): void => {
    isVisible.value = false
    isLoading.value = false
    hasLoaded.value = false
  }

  const setVisible = (visible: boolean): void => {
    isVisible.value = visible
  }

  return {
    isVisible,
    isLoading,
    hasLoaded,
    shouldLoad,
    startLoading,
    finishLoading,
    reset,
    setVisible
  }
}

export function useInfiniteScroll(callback: () => Promise<void>) {
  const isLoading = ref<boolean>(false)
  const hasMore = ref<boolean>(true)
  const page = ref<number>(1)

  const loadMore = async (): Promise<void> => {
    if (isLoading.value || !hasMore.value) return

    isLoading.value = true
    try {
      await callback()
      page.value++
    } catch (error) {
      console.error('Error loading more data:', error)
    } finally {
      isLoading.value = false
    }
  }

  const reset = (): void => {
    page.value = 1
    hasMore.value = true
    isLoading.value = false
  }

  const setHasMore = (value: boolean): void => {
    hasMore.value = value
  }

  return {
    isLoading,
    hasMore,
    page,
    loadMore,
    reset,
    setHasMore
  }
} 