// Dedicated polyfills for xlsx-populate
import { <PERSON><PERSON><PERSON> } from 'buffer'

// Ensure all required Node.js globals are available for xlsx-populate
if (typeof window !== 'undefined') {
  // Buffer polyfill
  if (!(window as any).Buffer) {
    (window as any).Buffer = Buffer
  }
  
  // Global polyfill
  if (!(window as any).global) {
    (window as any).global = window
  }
  
  // Process polyfill
  if (!(window as any).process) {
    (window as any).process = {
      env: {},
      version: '',
      versions: {},
      platform: 'browser',
      nextTick: (fn: any) => Promise.resolve().then(fn),
      browser: true
    }
  }
  
  // Crypto polyfill
  if (!window.crypto) {
    (window as any).crypto = {
      getRandomValues: (arr: any) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256)
        }
        return arr
      }
    }
  }
  
  // Stream polyfill
  if (!(window as any).stream) {
    (window as any).stream = {
      Readable: class Readable {
        constructor() {}
      },
      Writable: class Writable {
        constructor() {}
      }
    }
  }
  
  // Util polyfill
  if (!(window as any).util) {
    (window as any).util = {
      inherits: () => {},
      format: (str: string, ...args: any[]) => {
        return str.replace(/%s/g, () => args.shift() || '')
      }
    }
  }
  
  // Events polyfill
  if (!(window as any).events) {
    (window as any).events = {
      EventEmitter: class EventEmitter {
        constructor() {}
        on() {}
        emit() {}
      }
    }
  }
  
  // Path polyfill
  if (!(window as any).path) {
    (window as any).path = {
      join: (...args: string[]) => args.join('/'),
      resolve: (...args: string[]) => args.join('/'),
      dirname: (path: string) => path.split('/').slice(0, -1).join('/'),
      basename: (path: string) => path.split('/').pop() || ''
    }
  }
  
  // FS polyfill (minimal implementation)
  if (!(window as any).fs) {
    (window as any).fs = {
      readFileSync: () => { throw new Error('fs.readFileSync not available in browser') },
      writeFileSync: () => { throw new Error('fs.writeFileSync not available in browser') },
      existsSync: () => false
    }
  }
  
  // Zlib polyfill (if needed)
  if (!(window as any).zlib) {
    (window as any).zlib = {
      inflateSync: () => { throw new Error('zlib.inflateSync not available in browser') },
      deflateSync: () => { throw new Error('zlib.deflateSync not available in browser') }
    }
  }
  
  // Console polyfill (ensure it exists)
  if (!(window as any).console) {
    (window as any).console = console
  }
}

// Function to verify all polyfills are loaded
export function verifyPolyfills() {
  if (typeof window === 'undefined') return true
  
  const required = ['Buffer', 'process', 'global', 'crypto']
  const missing = required.filter(key => !(window as any)[key])
  
  if (missing.length > 0) {
    console.error('Missing polyfills:', missing)
    return false
  }
  
  return true
}

export { Buffer } 