// Format currency based on locale
export const formatCurrency = (value: number, locale?: string): string => {
  const currentLocale = locale || 'vi-VN'
  
  const currencyConfig: Record<string, { currency: string, locale: string }> = {
    'vi': { currency: 'VND', locale: 'vi-VN' },
    'zh': { currency: 'CNY', locale: 'zh-CN' },
    'en': { currency: 'USD', locale: 'en-US' }
  }
  
  const config = currencyConfig[currentLocale] || currencyConfig['vi']
  
  return new Intl.NumberFormat(config.locale, {
    style: 'currency',
    currency: config.currency
  }).format(value)
}

// Format date based on locale
export const formatDate = (dateString: string, locale?: string): string => {
  const currentLocale = locale || 'vi-VN'
  
  const localeConfig: Record<string, string> = {
    'vi': 'vi-VN',
    'zh': 'zh-CN',
    'en': 'en-US'
  }
  
  const configLocale = localeConfig[currentLocale] || 'vi-VN'
  return new Date(dateString).toLocaleDateString(configLocale)
}

// Format datetime based on locale
export const formatDateTime = (dateString: string, locale?: string): string => {
  const currentLocale = locale || 'vi-VN'
  
  const localeConfig: Record<string, string> = {
    'vi': 'vi-VN',
    'zh': 'zh-CN',
    'en': 'en-US'
  }
  
  const configLocale = localeConfig[currentLocale] || 'vi-VN'
  return new Date(dateString).toLocaleString(configLocale)
}

// Get order status type for Element Plus tag
export const getOrderStatusType = (status: string): string => {
  const types: Record<string, string> = {
    'PENDING': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return types[status] || 'info'
}

// Get order status text in Vietnamese
export const getOrderStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    'PENDING': 'Chờ xử lý',
    'IN_PROGRESS': 'Đang xử lý',
    'COMPLETED': 'Hoàn thành',
    'CANCELLED': 'Đã hủy'
  }
  return texts[status] || status
}

// Generate random ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9)
}

// Debounce function
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Validate email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate phone number (Vietnamese format)
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /(84|0[3|5|7|8|9])+([0-9]{8})\b/
  return phoneRegex.test(phone)
}

export function camelToSnake(str: string) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

export function toSnakeCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase)
  } else if (obj !== null && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj).map(([k, v]) => [camelToSnake(k), toSnakeCase(v)])
    )
  }
  return obj
}

export function generateBOMCode(productCode: string, materialCode: string): string {
  // Format: BOM_{productCode}_{materialCode}
  return `BOM_${productCode}_${materialCode}`
}

export function generateBOMCodeSimple(productCode: string, materialCode: string): string {
  // Format đơn giản: {productCode}_{materialCode}
  return `${productCode}_${materialCode}`
} 


export function generateBOMCodeHard(productCode: string): string {
  // Format đơn giản: {productCode}_{materialCode}
  return `${productCode}`
} 
function toCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase)
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = key.replace(/_([a-z])/g, (_, c) => c.toUpperCase())
      acc[camelKey] = toCamelCase(obj[key])
      return acc
    }, {} as any)
  }
  return obj
}

export { toCamelCase } 