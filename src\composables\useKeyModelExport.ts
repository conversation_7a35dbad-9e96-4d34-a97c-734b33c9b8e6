import { ref } from 'vue'
import XlsxPopulate from 'xlsx-populate'
import { ElMessage } from 'element-plus'
import type { KeyModelRecord } from '@/api/keymodel'
import '../utils/xlsx-polyfills'
import { verifyPolyfills } from '../utils/xlsx-polyfills'

export function useKeyModelExport() {
  const exporting = ref(false)

  // Xuất nhiều records (giữ nguyên function cũ)
  async function exportToExcel(records: KeyModelRecord[], filename?: string) {
    try {
      exporting.value = true

      if (!records.length) {
        ElMessage.warning('<PERSON>hông có dữ liệu để xuất')
        return
      }

      await exportRecords(records, filename)
      
    } catch (error) {
      console.error('Export error:', error)
      ElMessage.error(`Xuất Excel lỗi: ${error instanceof Error ? error.message : error}`)
    } finally {
      exporting.value = false
    }
  }

  // Xuất 1 record theo ID
  async function exportSingleRecord(record: KeyModelRecord, filename?: string) {
    try {
      exporting.value = true

      if (!record) {
        ElMessage.warning('Không có dữ liệu để xuất')
        return
      }

      const defaultFilename = `key-model-${record.colorwayId || record.id}-${new Date().toISOString().split('T')[0]}.xlsx`
      await exportRecords([record], filename || defaultFilename)
      
    } catch (error) {
      console.error('Export single record error:', error)
      ElMessage.error(`Xuất Excel lỗi: ${error instanceof Error ? error.message : error}`)
    } finally {
      exporting.value = false
    }
  }

  // Function chung để xuất records
  async function exportRecords(records: KeyModelRecord[], filename?: string) {
    try {
      // Verify polyfills are available
      if (!verifyPolyfills()) {
        throw new Error('Required polyfills not loaded. Please refresh the page and try again.')
      }
      
      // Load template
      const templateRes = await fetch('/key-model-template.xlsx')
      if (!templateRes.ok) throw new Error('Không tải được template')
      const templateBuffer = await templateRes.arrayBuffer()
      
      const workbook = await XlsxPopulate.fromDataAsync(templateBuffer)

      // Helper functions
      const formatDate = (date?: string | null) =>
        date ? new Date(date).toLocaleDateString('vi-VN') : ''

      // Tạo sheet cho từng record
      records.forEach((record, index) => {
        if (index === 0) {
          // Sử dụng sheet đầu tiên cho record đầu tiên
          fillRecordData(workbook.sheet(0), record, formatDate)
        } else {
          // Tạo sheet mới cho các record tiếp theo
          const newSheet = workbook.addSheet(`${record.colorwayId || `Record ${index + 1}`}`)
          
          // Copy template từ sheet đầu tiên
          const templateSheet = workbook.sheet(0)
          copySheetContent(templateSheet, newSheet)
          
          // Fill data vào sheet mới
          fillRecordData(newSheet, record, formatDate)
        }
      })

      // Export file
      const defaultFilename = `key-model-records-${new Date().toISOString().split('T')[0]}.xlsx`
      const finalFilename = filename || defaultFilename

      try {
        let blob: Blob
        
        try {
          // Try to get output as ArrayBuffer first (most reliable)
          const output = await workbook.outputAsync({ type: 'arraybuffer' })
          blob = new Blob([output as ArrayBuffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
        } catch (outputError) {
          console.warn('ArrayBuffer output failed, trying default output:', outputError)
          // Fallback to default output
          const output = await workbook.outputAsync()
          if (output instanceof Blob) {
            blob = output
          } else if (output instanceof ArrayBuffer) {
            blob = new Blob([output], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
          } else {
            throw new Error('Unexpected output format from xlsx-populate')
          }
        }

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = finalFilename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success(`Xuất Excel thành công! File: ${finalFilename}`)
      } catch (exportError) {
        console.error('Export blob error:', exportError)
        throw new Error(`Lỗi tạo file Excel: ${exportError instanceof Error ? exportError.message : exportError}`)
      }
    } catch (error) {
      console.error('Export records error:', error)
      throw error
    }
  }

  // Function copy content từ template sheet sang sheet mới
  function copySheetContent(sourceSheet: any, targetSheet: any) {
    // Copy cell values và formatting
    sourceSheet.usedRange().forEach((cell: any) => {
      const address = cell.address()
      const targetCell = targetSheet.cell(address)
      
      // Copy value
      targetCell.value(cell.value())
      
      // Copy formatting
      if (cell.style()) {
        targetCell.style(cell.style())
      }
      
      // Copy fill color
      if (cell.style('fill')) {
        targetCell.style('fill', cell.style('fill'))
      }
    })
  }

  // Function fill data vào sheet
  function fillRecordData(sheet: any, record: KeyModelRecord, formatDate: Function) {
     // Basic Information

     sheet.cell('E3').value(record.colorwayId)
     sheet.cell('E5').value(record.materialId)
     sheet.cell('E7').value(record.keyModelType)
     sheet.cell('K3').value(record.productName)
     sheet.cell('K5').value(record.fty)
     sheet.cell('K7').value(record.quantity)
     sheet.cell('Q3').value(record.isd)
     sheet.cell('Q5').value(record.season)
     sheet.cell('Q7').value(formatDate(record.poReceivedDate))
 
     sheet.cell('H9').fill = {
       type: 'pattern',
       pattern: 'solid',
       fgColor: { argb: 'FFF79646' } // Màu cam RGB(247,150,70)
     }
 
 
     // Product Creation Stage
     sheet.cell('E11').value = formatDate(record.updateDate)

     sheet.cell('G11').value(record.poReceived)
     sheet.cell('G12').value(record.pfcValidated)
     sheet.cell('G10').value(record.ptrssCfmed)

     sheet.cell('E15').value(record.designKeyFeature || '')
     sheet.cell('E20').value(record.devLearning || '')
     sheet.cell('E26').value(record.hrMaterialUpdate || '')
     sheet.cell('E31').value(record.gtmComment || '')
 
     // Commercial Stage - EST
     sheet.cell('K11').value(formatDate(record.estDate))
     sheet.cell('M10').value(record.firstEstPass)
     sheet.cell('M11').value(record.secondEstPass)
     sheet.cell('M12').value(record.fittingApproval)
     sheet.cell('M13').value(record.ptrssTestedEst)
     sheet.cell('I15').value(record.estComment || '')
 
     // Commercial Stage - FST
     sheet.cell('K22').value(formatDate(record.sampleReceivedDate))
     sheet.cell('K24').value(formatDate(record.fstDate))
     sheet.cell('M21').value(record.firstFstPass)
     sheet.cell('M22').value(record.secondFstPass)
     sheet.cell('M24').value(record.ptrssTestedFst)
     sheet.cell('M26').value(record.pfcCfmed)
     sheet.cell('M27').value(record.toolingCfmed)
     sheet.cell('I31').value(record.fstComment || '')
 
     // Manufacturing Stage - PROD MATERIAL
     sheet.cell('Q11').value(formatDate(record.prodMaterialUpdateDate))
     sheet.cell('S10').value(record.physicalTestPass)
     sheet.cell('S11').value(record.visualCheckPass)
     sheet.cell('O15').value(record.prodMaterialComment || '')
 
     // Manufacturing Stage - MPPA Process
     sheet.cell('Q22').value(formatDate(record.processAssessmentDate))
     sheet.cell('S21').value(record.mppaCheckProcess)
     sheet.cell('S22').value(record.mppaPassProcess)
     sheet.cell('O26').value(record.processComment || '')
 
     // Manufacturing Stage - MPPA Product
     sheet.cell('Q32').value(formatDate(record.productAssessmentDate))
     sheet.cell('S32').value(record.mppaCheckProduct)
     sheet.cell('S34').value(record.mppaPassProduct)
     sheet.cell('O36').value(record.productComment || '')
 
  }

  return {
    exporting,
    exportToExcel,
    exportSingleRecord
  }
}


