import api from './axios'
import type { 
  KFXXZLDistinctResponse, 
  Colorway, 
  Material, 
  Season, 
  Factory,
  KFXXZLFilterOptions,
  KFXXZLAdvancedFilterOptions
} from '@/types/filter'

// KFXXZL Distinct Value API Functions
// Based on the KFXXZL Distinct APIs documentation

/**
 * Get distinct colorways from the kfxxzl table
 * @returns Promise with distinct colorway IDs
 */
export const getDistinctColorways = async (): Promise<KFXXZLDistinctResponse<string[]>> => {
  try {
    const response = await api.get('/colorways')
    return response.data
  } catch (error) {
    console.error('Failed to fetch distinct colorways:', error)
    throw error
  }
}

/**
 * Get distinct materials from the kfxxzl table
 * @returns Promise with distinct material IDs
 */
export const getDistinctMaterials = async (): Promise<KFXXZLDistinctResponse<string[]>> => {
  try {
    const response = await api.get('/materials')
    return response.data
  } catch (error) {
    console.error('Failed to fetch distinct materials:', error)
    throw error
  }
}

/**
 * Get distinct seasons from the kfxxzl table
 * @returns Promise with distinct seasons
 */
export const getDistinctSeasons = async (): Promise<KFXXZLDistinctResponse<string[]>> => {
  try {
    const response = await api.get('/seasons')
    return response.data
  } catch (error) {
    console.error('Failed to fetch distinct seasons:', error)
    throw error
  }
}

/**
 * Get distinct factories from the kfxxzl table
 * @returns Promise with distinct factories
 */
export const getDistinctFactories = async (): Promise<KFXXZLDistinctResponse<string[]>> => {
  try {
    const response = await api.get('/factories')
    return response.data
  } catch (error) {
    console.error('Failed to fetch distinct factories:', error)
    throw error
  }
}

/**
 * Get all distinct values at once (colorways, materials, seasons, factories)
 * @returns Promise with all distinct values
 */
export const getAllDistinctValues = async (): Promise<{
  colorways: string[]
  materials: string[]
  seasons: string[]
  factories: string[]
}> => {
  try {
    const [colorwaysRes, materialsRes, seasonsRes, factoriesRes] = await Promise.all([
      getDistinctColorways(),
      getDistinctMaterials(),
      getDistinctSeasons(),
      getDistinctFactories()
    ])

    return {
      colorways: colorwaysRes.data || [],
      materials: materialsRes.data || [],
      seasons: seasonsRes.data || [],
      factories: factoriesRes.data || []
    }
  } catch (error) {
    console.error('Failed to fetch all distinct values:', error)
    throw error
  }
}

/**
 * Get distinct values for a specific field
 * @param field - The field to get distinct values for
 * @returns Promise with distinct values for the specified field
 */
export const getDistinctValuesByField = async (
  field: 'colorwayId' | 'materialId' | 'season' | 'factory'
): Promise<string[]> => {
  try {
    let response: KFXXZLDistinctResponse<string[]>
    
    switch (field) {
      case 'colorwayId':
        response = await getDistinctColorways()
        break
      case 'materialId':
        response = await getDistinctMaterials()
        break
      case 'season':
        response = await getDistinctSeasons()
        break
      case 'factory':
        response = await getDistinctFactories()
        break
      default:
        throw new Error(`Invalid field: ${field}`)
    }
    
    return response.data || []
  } catch (error) {
    console.error(`Failed to fetch distinct values for field ${field}:`, error)
    throw error
  }
}

/**
 * Search distinct values with filters
 * @param filters - Filter options for the search
 * @returns Promise with filtered distinct values
 */
export const searchDistinctValues = async (
  filters: KFXXZLFilterOptions
): Promise<{
  colorways: string[]
  materials: string[]
  seasons: string[]
  factories: string[]
}> => {
  try {
    // Build query parameters
    const params = new URLSearchParams()
    
    if (filters.search) {
      params.append('search', filters.search)
    }
    
    if (filters.pageNumber) {
      params.append('pageNumber', filters.pageNumber.toString())
    }
    
    if (filters.pageSize) {
      params.append('pageSize', filters.pageSize.toString())
    }

    // Get all distinct values with filters
    const [colorwaysRes, materialsRes, seasonsRes, factoriesRes] = await Promise.all([
      api.get(`/colorways?${params.toString()}`),
      api.get(`/materials?${params.toString()}`),
      api.get(`/seasons?${params.toString()}`),
      api.get(`/factories?${params.toString()}`)
    ])

    return {
      colorways: colorwaysRes.data?.data || [],
      materials: materialsRes.data?.data || [],
      seasons: seasonsRes.data?.data || [],
      factories: factoriesRes.data?.data || []
    }
  } catch (error) {
    console.error('Failed to search distinct values:', error)
    throw error
  }
}

/**
 * Get distinct values with advanced filtering
 * @param options - Advanced filter options
 * @returns Promise with filtered distinct values
 */
export const getDistinctValuesWithAdvancedFilters = async (
  options: KFXXZLAdvancedFilterOptions
): Promise<{
  colorways: string[]
  materials: string[]
  seasons: string[]
  factories: string[]
}> => {
  try {
    // Build advanced filter query
    const params = new URLSearchParams()
    
    if (options.search) {
      params.append('search', options.search)
    }
    
    if (options.pageNumber) {
      params.append('pageNumber', options.pageNumber.toString())
    }
    
    if (options.pageSize) {
      params.append('pageSize', options.pageSize.toString())
    }
    
    if (options.sortBy) {
      params.append('sortBy', options.sortBy)
    }
    
    if (options.sortOrder) {
      params.append('sortOrder', options.sortOrder)
    }

    // Add advanced filters
    if (options.filters && options.filters.length > 0) {
      options.filters.forEach((filter, index) => {
        params.append(`filters[${index}][field]`, filter.field)
        params.append(`filters[${index}][operator]`, filter.operator)
        if (Array.isArray(filter.value)) {
          filter.value.forEach((val, valIndex) => {
            params.append(`filters[${index}][value][${valIndex}]`, val)
          })
        } else {
          params.append(`filters[${index}][value]`, filter.value)
        }
      })
    }

    // Get all distinct values with advanced filters
    const [colorwaysRes, materialsRes, seasonsRes, factoriesRes] = await Promise.all([
      api.get(`/colorways?${params.toString()}`),
      api.get(`/materials?${params.toString()}`),
      api.get(`/seasons?${params.toString()}`),
      api.get(`/factories?${params.toString()}`)
    ])

    return {
      colorways: colorwaysRes.data?.data || [],
      materials: materialsRes.data?.data || [],
      seasons: seasonsRes.data?.data || [],
      factories: factoriesRes.data?.data || []
    }
  } catch (error) {
    console.error('Failed to get distinct values with advanced filters:', error)
    throw error
  }
}

/**
 * Cache distinct values for better performance
 * @param ttl - Time to live in milliseconds (default: 5 minutes)
 * @returns Promise with cached distinct values
 */
export const getCachedDistinctValues = (() => {
  let cache: {
    data: {
      colorways: string[]
      materials: string[]
      seasons: string[]
      factories: string[]
    } | null
    timestamp: number | null
    ttl: number
  } = {
    data: null,
    timestamp: null,
    ttl: 5 * 60 * 1000 // 5 minutes default
  }

  return async (ttl?: number): Promise<{
    colorways: string[]
    materials: string[]
    seasons: string[]
    factories: string[]
  }> => {
    const now = Date.now()
    const cacheTtl = ttl || cache.ttl

    // Check if cache is valid
    if (cache.data && cache.timestamp && (now - cache.timestamp) < cacheTtl) {
      return cache.data
    }

    // Fetch fresh data
    try {
      const data = await getAllDistinctValues()
      
      // Update cache
      cache.data = data
      cache.timestamp = now
      cache.ttl = cacheTtl
      
      return data
    } catch (error) {
      console.error('Failed to fetch cached distinct values:', error)
      
      // Return cached data if available, even if expired
      if (cache.data) {
        return cache.data
      }
      
      throw error
    }
  }
})()

/**
 * Clear the distinct values cache
 */
export const clearDistinctValuesCache = (): void => {
  // This would be implemented if we had a proper cache mechanism
  console.log('Distinct values cache cleared')
}

// All API functions are exported above
