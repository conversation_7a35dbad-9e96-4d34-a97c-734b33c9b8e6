<template>
  <el-dialog v-model="dialogVisible" title="Chi tiết Key Model" width="90%" :before-close="handleClose"
    class="keymodel-details-dialog">
    <div v-if="record" class="keymodel-details">
      <!-- Basic Information Section -->
      <el-card class="basic-info-card" shadow="never">

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="detail-item">
              <label>Colorway ID:</label>
              <span>{{ record.colorwayId }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>Material ID:</label>
              <span>{{ record.materialId }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>Key Model Type:</label>
              <span>{{ record.keyModelType }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="detail-item">
              <label>Product Name:</label>
              <span>{{ record.productName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>Factory:</label>
              <span>{{ record.fty }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>Quantity:</label>
              <span>{{ record.quantity }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="detail-item">
              <label>ISD:</label>
              <span>{{ record.isd }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>Season:</label>
              <span>{{ record.season }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>PO Received Date:</label>
              <span>{{ formatDate(record.poReceivedDate) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- Stage Information -->
      <el-row :gutter="24" style="margin-top: 24px">
        <!-- Product Creation Section -->
        <el-col :span="8">
          <div class="details-section">
            <h3 class="section-title product">PRODUCT CREATION</h3>

            <div class="subsection">
              <h4 class="subsection-title">GTMF RFC UPDATE</h4>
              <div class="detail-item">
                <label>Update Date:</label>
                <span>{{ formatDate(record.updateDate) }}</span>
              </div>

              <div class="detail-item">
                <label>Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.poReceived ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.poReceived" />
                      <Close v-else />
                    </el-icon>
                    <span>PO Received</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.pfcValidated ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.pfcValidated" />
                      <Close v-else />
                    </el-icon>
                    <span>PFC Validated</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.ptrssCfmed ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.ptrssCfmed" />
                      <Close v-else />
                    </el-icon>
                    <span>PTRSS CFM'ed</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">DESIGN KEY FEATURE</h4>
              <div class="detail-item">
                <span class="textarea-content">{{ record.designKeyFeature || 'N/A' }}</span>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">DEV LEARNING</h4>
              <div class="detail-item">
                <span class="textarea-content">{{ record.devLearning || 'N/A' }}</span>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">HR MATERIAL UPDATE</h4>
              <div class="detail-item">
                <span>{{ record.hrMaterialUpdate || 'N/A' }}</span>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">GTM COMMENT</h4>
              <div class="detail-item">
                <span class="textarea-content">{{ record.gtmComment || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </el-col>

        <!-- Commercial Stage Section -->
        <el-col :span="8">
          <div class="details-section">
            <h3 class="section-title commercial">COMMERCIAL STAGE</h3>

            <div class="subsection">
              <h4 class="subsection-title">EST UPDATE</h4>
              <div class="detail-item">
                <label>EST Date:</label>
                <span>{{ formatDate(record.estDate) }}</span>
              </div>

              <div class="detail-item">
                <label>EST Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.firstEstPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.firstEstPass" />
                      <Close v-else />
                    </el-icon>
                    <span>First EST Pass</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.secondEstPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.secondEstPass" />
                      <Close v-else />
                    </el-icon>
                    <span>Second EST Pass</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.fittingApproval ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.fittingApproval" />
                      <Close v-else />
                    </el-icon>
                    <span>Fitting Approval</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.ptrssTestedEst ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.ptrssTestedEst" />
                      <Close v-else />
                    </el-icon>
                    <span>PTRSS Tested EST</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">FST UPDATE</h4>
              <div class="detail-item">
                <label>Sample Received Date:</label>
                <span>{{ formatDate(record.sampleReceivedDate) }}</span>
              </div>

              <div class="detail-item">
                <label>FST Date:</label>
                <span>{{ formatDate(record.fstDate) }}</span>
              </div>

              <div class="detail-item">
                <label>FST Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.firstFstPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.firstFstPass" />
                      <Close v-else />
                    </el-icon>
                    <span>First FST Pass</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.secondFstPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.secondFstPass" />
                      <Close v-else />
                    </el-icon>
                    <span>Second FST Pass</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.ptrssTestedFst ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.ptrssTestedFst" />
                      <Close v-else />
                    </el-icon>
                    <span>PTRSS Tested FST</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.pfcCfmed ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.pfcCfmed" />
                      <Close v-else />
                    </el-icon>
                    <span>PFC CFM'ed</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.toolingCfmed ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.toolingCfmed" />
                      <Close v-else />
                    </el-icon>
                    <span>Tooling CFM'ed</span>
                  </div>
                </div>
              </div>

              <div class="detail-item">
                <label>Comment:</label>
                <span class="textarea-content">{{ record.fstComment || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </el-col>

        <!-- Manufacturing Stage Section -->
        <el-col :span="8">
          <div class="details-section">
            <h3 class="section-title manufacturing">MANUFACTURING STAGE</h3>

            <div class="subsection">
              <h4 class="subsection-title">PROD MATERIAL UPDATE</h4>
              <div class="detail-item">
                <label>Update Date:</label>
                <span>{{ formatDate(record.prodMaterialUpdateDate) }}</span>
              </div>

              <div class="detail-item">
                <label>Material Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.physicalTestPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.physicalTestPass" />
                      <Close v-else />
                    </el-icon>
                    <span>Physical Test Pass</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.visualCheckPass ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.visualCheckPass" />
                      <Close v-else />
                    </el-icon>
                    <span>Visual Check Pass</span>
                  </div>
                </div>
              </div>

              <div class="detail-item">
                <label>Comment:</label>
                <span class="textarea-content">{{ record.prodMaterialComment || 'N/A' }}</span>
              </div>
            </div>

            <div class="subsection">
              <h4 class="subsection-title">MPPA UPDATE</h4>
              <div class="detail-item">
                <label>Process Assessment Date:</label>
                <span>{{ formatDate(record.processAssessmentDate) }}</span>
              </div>

              <div class="detail-item">
                <label>Process Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.mppaCheckProcess ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.mppaCheckProcess" />
                      <Close v-else />
                    </el-icon>
                    <span>MPPA Check Process</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.mppaPassProcess ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.mppaPassProcess" />
                      <Close v-else />
                    </el-icon>
                    <span>MPPA Pass Process</span>
                  </div>
                </div>
              </div>

              <div class="detail-item">
                <label>Process Comment:</label>
                <span class="textarea-content">{{ record.processComment || 'N/A' }}</span>
              </div>

              <div class="detail-item">
                <label>Product Assessment Date:</label>
                <span>{{ formatDate(record.productAssessmentDate) }}</span>
              </div>

              <div class="detail-item">
                <label>Product Checks:</label>
                <div class="checkbox-display">
                  <div class="checkbox-item">
                    <el-icon :color="record.mppaCheckProduct ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.mppaCheckProduct" />
                      <Close v-else />
                    </el-icon>
                    <span>MPPA Check Product</span>
                  </div>
                  <div class="checkbox-item">
                    <el-icon :color="record.mppaPassProduct ? '#67c23a' : '#c0c4cc'">
                      <Check v-if="record.mppaPassProduct" />
                      <Close v-else />
                    </el-icon>
                    <span>MPPA Pass Product</span>
                  </div>
                </div>
              </div>

              <div class="detail-item">
                <label>Product Comment:</label>
                <span class="textarea-content">{{ record.productComment || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Đóng</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'
import type { KeyModelRecord } from '@/api/keymodel'

interface Props {
  visible: boolean
  record: KeyModelRecord | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = ref(false)

// Watch for visibility changes
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Helper function to format dates
const formatDate = (date: string | null | undefined) => {
  if (!date) return 'N/A'
  try {
    return new Date(date).toLocaleDateString('vi-VN')
  } catch {
    return 'N/A'
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.keymodel-details-dialog :deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.keymodel-details {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 10px;
  overflow-x: hidden;
}

.basic-info-card {
  margin-bottom: 0;
}

.basic-info-card :deep(.el-card__header) {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.basic-info-card h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.details-section {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  height: 100%;
  background: var(--el-bg-color-page);
}

.section-title {
  margin: 0 0 16px 0;
  padding: 8px 12px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.section-title.product {
  background: #16a085;
}

.section-title.commercial {
  background: #f39c12;
}

.section-title.manufacturing {
  background: #2980b9;
}

.subsection {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.subsection:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subsection-title {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: bold;
  color: var(--el-text-color-regular);
  text-transform: uppercase;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #909399;
  margin-bottom: 4px;
}

.detail-item span {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.textarea-content {
  display: block;
  white-space: pre-wrap;
  word-break: break-word;
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  min-height: 60px;
}

.checkbox-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

/* Dark mode support */
:deep(.dark) .details-section {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:deep(.dark) .subsection {
  border-color: var(--el-border-color);
}

:deep(.dark) .textarea-content {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
  color: var(--el-text-color-primary);
}
</style>
