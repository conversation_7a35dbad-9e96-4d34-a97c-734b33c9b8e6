<template>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="/product-management.png" alt="Logo" class="logo" />
                <h1>ERP System</h1>
                <p>Modern Enterprise Resource Planning System</p>
            </div>

            <el-form
                ref="loginFormRef"
                :model="loginForm"
                :rules="loginRules"
                class="login-form"
                @submit.prevent="handleLogin"
            >
                <el-form-item prop="userID">
                    <el-input
                        v-model="loginForm.userID"
                        placeholder="User ID"
                        size="large"
                        prefix-icon="User"
                        @keyup.enter="handleLogin"
                    />
                </el-form-item>

                <el-form-item prop="password">
                    <el-input
                        v-model="loginForm.password"
                        type="password"
                        placeholder="Password"
                        size="large"
                        prefix-icon="Lock"
                        show-password
                        @keyup.enter="handleLogin"
                    />
                </el-form-item>

                <el-form-item>
                    <el-button
                        type="primary"
                        size="large"
                        class="login-button"
                        :loading="authStore.loading"
                        @click="handleLogin"
                    >
                        {{ authStore.loading ? "Logging in..." : "Login" }}
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useRouter } from "vue-router";
import { ElNotification } from "element-plus";
import { useAuthStore } from "@/stores/auth";
import type { FormInstance, FormRules } from "element-plus";

const router = useRouter();
const authStore = useAuthStore();

const loginFormRef = ref<FormInstance>();
const loginForm = reactive({
    userID: "",
    password: "",
});

const loginRules: FormRules = {
    userID: [
        {
            required: true,
            message: "Please enter your User ID",
            trigger: "blur",
        },
        {
            min: 3,
            message: "User ID must be at least 3 characters",
            trigger: "blur",
        },
    ],
    password: [
        {
            required: true,
            message: "Please enter your password",
            trigger: "blur",
        },
    ],
};

async function handleLogin() {
    if (!loginFormRef.value) return;
    try {
        const valid = await loginFormRef.value.validate();
        if (!valid) return;
        const success = await authStore.login(loginForm);
        if (success) {
            // Show success notification
            ElNotification({
                title: "Success",
                message: "Login successful!",
                type: "success",
                duration: 3000,
            });
            router.push("/key-model-tracking");
            // Reset form
            loginForm.userID = "";
            loginForm.password = "";
            loginFormRef.value?.clearValidate();
        }
    } catch (error: any) {
        // Show error notification
        ElNotification({
            title: "Login Failed",
            message: error.message || "An error occurred during login",
            type: "error",
            duration: 5000,
        });
    }
}

// Watch for auth store errors and show notifications
watch(
    () => authStore.error,
    (error) => {
        if (error) {
            ElNotification({
                title: "Login Failed",
                message: error,
                type: "error",
                duration: 5000,
            });
            // Clear the error from store after showing notification
            authStore.clearError();
        }
    },
);

// Clear error when form changes
watch(
    () => [loginForm.userID, loginForm.password],
    () => {
        if (authStore.error) authStore.clearError();
    },
);
</script>

<style scoped>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;
}

.login-header h1 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 28px;
    font-weight: 600;
}

.login-header p {
    margin: 0;
    color: #606266;
    font-size: 14px;
}

.login-form {
    margin-top: 20px;
}

.login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
}

/* Dark mode */
:deep(.dark) .login-card {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color);
}

:deep(.dark) .login-header h1 {
    color: var(--el-text-color-primary);
}

:deep(.dark) .login-header p {
    color: var(--el-text-color-regular);
}

/* Input text color override */
:deep(.el-input__inner) {
    color: #000000 !important;
}

:deep(.el-input__inner::placeholder) {
    color: #999999 !important;
}
</style>
