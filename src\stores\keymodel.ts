
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { keyModelAPI, type KeyModelRecord, type KeyModelFilterOptions, type Season, type Factory, type KeyModelType } from '@/api/keymodel'
import { ElMessage } from 'element-plus'
import { useAuthStore } from './auth'

export const useKeyModelStore = defineStore('keyModel', () => {
  // State
  const records = ref<KeyModelRecord[]>([])
  const filteredRecords = ref<KeyModelRecord[]>([])
  const currentRecord = ref<KeyModelRecord | null>(null)
  const seasons = ref<Season[]>([])
  const factories = ref<Factory[]>([])
  const keyModelTypes = ref<KeyModelType[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Pagination
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  
  // Filters
  const filters = ref<KeyModelFilterOptions>({})

  // Computed
  const hasRecords = computed(() => records.value.length > 0)
  const hasFilteredRecords = computed(() => filteredRecords.value.length > 0)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // Key Model Records Actions
  const fetchRecords = async (filterOptions?: KeyModelFilterOptions) => {
    try {
      setLoading(true)
      clearError()
      
      const response = await keyModelAPI.getAll(filterOptions)
      if (response.data.code === 200) {
        records.value = response.data.data || []
        filteredRecords.value = records.value // Initialize filtered records

      } else {
        throw new Error(response.data.message || 'Failed to fetch records')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Lỗi khi tải dữ liệu: ${message}`)
    } finally {
      setLoading(false)
    }
  }

  const fetchPaginatedRecords = async (filterOptions?: KeyModelFilterOptions) => {
    try {
      setLoading(true)
      clearError()
      
      const paginationFilters = {
        ...filterOptions,
        pageNumber: currentPage.value,
        pageSize: pageSize.value
      }
      
      const response = await keyModelAPI.getPaginated(paginationFilters)
      if (response.data.code === 200) {
        records.value = response.data.data || []
        // Note: API should return total count for proper pagination
      } else {
        throw new Error(response.data.message || 'Failed to fetch paginated records')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Error fetching data: ${message}`)
    } finally {
      setLoading(false)
    }
  }

  const fetchRecordById = async (id: string) => {
    try {
      setLoading(true)
      clearError()
      
      const response = await keyModelAPI.getById(id)
      if (response.data.code === 200) {
        currentRecord.value = response.data.data
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Failed to fetch record')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Error fetching record: ${message}`)
      return null
    } finally {
      setLoading(false)
    }
  }

  const createRecord = async (data: Omit<KeyModelRecord, 'id' | 'createdDate' | 'updatedDate'>) => {
    try {
      setLoading(true)
      clearError()
      
      // Lấy UserID từ localStorage trực tiếp
      const userDataStr = localStorage.getItem('user-data')
      const userData = userDataStr ? JSON.parse(userDataStr) : null
      
      
      const recordData = {
        ...data,
        userId: userData?.userID || '', // Giữ nguyên string
        createdBy: userData?.userName || 'Unknown'
      }
      
      console.log('Record Data being sent:', recordData) // Debug log
      
      const response = await keyModelAPI.create(recordData)
      if (response.data.code === 200) {
        const newRecord = response.data.data
        records.value.unshift(newRecord)
        ElMessage.success('Create record successfully!')
        return newRecord
      } else {
        throw new Error(response.data.message || 'Failed to create record')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Error creating record: ${message}`)
      return null
    } finally {
      setLoading(false)
    }
  }

  const updateRecord = async (id: string, data: Partial<KeyModelRecord>) => {
    try {
      setLoading(true)
      clearError()
      
      // Lấy UserID từ localStorage trực tiếp
      const userDataStr = localStorage.getItem('user-data')
      const userData = userDataStr ? JSON.parse(userDataStr) : null
      
      
      const updateData = {
        ...data,
        userId: userData?.userID || '', // Giữ nguyên string
        updatedBy: userData?.userName || 'Unknown'
      }
      
      
      const response = await keyModelAPI.update(id, updateData)
      if (response.data.code === 200) {
        const updatedRecord = response.data.data
        const index = records.value.findIndex(r => r.id === id)
        if (index !== -1) {
          records.value[index] = updatedRecord
        }
        if (currentRecord.value?.id === id) {
          currentRecord.value = updatedRecord
        }
        ElMessage.success('Update record successfully!')
        return updatedRecord
      } else {
        throw new Error(response.data.message || 'Failed to update record')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Error updating record: ${message}`)
      return null
    } finally {
      setLoading(false)
    }
  }

  const deleteRecord = async (id: string) => {
    try {
      setLoading(true)
      clearError()
      
      const response = await keyModelAPI.delete(id)
      if (response.data.code === 200) {
        // Clear current record if it's the deleted one
        if (currentRecord.value?.id === id) {
          currentRecord.value = null
        }
        ElMessage.success('Delete record successfully!')
        return true
      } else {
        throw new Error(response.data.message || 'Failed to delete record')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(message)
      ElMessage.error(`Error deleting record: ${message}`)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Lookup Data Actions
  const fetchSeasons = async () => {
    try {
      const response = await keyModelAPI.seasons.getAll()
      if (response.data.code === 200) {
        seasons.value = response.data.data || []
      }
    } catch (err) {
      console.error('Failed to fetch seasons:', err)
    }
  }

  const fetchFactories = async () => {
    try {
      const response = await keyModelAPI.factories.getAll()
      if (response.data.code === 200) {
        factories.value = response.data.data || []
      }
    } catch (err) {
      console.error('Failed to fetch factories:', err)
    }
  }

  const fetchKeyModelTypes = async () => {
    try {
      const response = await keyModelAPI.keyModelTypes.getAll()
      if (response.data.code === 200) {
        keyModelTypes.value = response.data.data || []
      }
    } catch (err) {
      console.error('Failed to fetch key model types:', err)
    }
  }

//   const fetchAllLookupData = async () => {
//     await Promise.all([
//       fetchSeasons(),
//       fetchFactories(),
//       fetchKeyModelTypes()
//     ])
//   }

  const getDistinctValues = async (field: 'colorwayId' | 'materialId' | 'keyModelType' | 'productName' | 'isd' | 'fty' | 'season') => {
    try {
      const response = await keyModelAPI.getDistinct(field)
      if (response.data.code === 200) {
        return response.data.data || []
      }
      return []
    } catch (err) {
      console.error(`Failed to fetch distinct values for ${field}:`, err)
      return []
    }
  }

  // Filter Actions
  const setFilters = (newFilters: KeyModelFilterOptions) => {
    filters.value = { ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {}
    filteredRecords.value = []
  }

  const applyFilters = async () => {
    await fetchRecords(filters.value)
  }

  const setFilteredRecords = (records: KeyModelRecord[]) => {
    filteredRecords.value = records
  }

  // Pagination Actions
  const setPage = (page: number) => {
    currentPage.value = page
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // Reset to first page
  }

  const nextPage = () => {
    currentPage.value++
  }

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }

  // Reset Actions
  const resetStore = () => {
    records.value = []
    currentRecord.value = null
    seasons.value = []
    factories.value = []
    keyModelTypes.value = []
    loading.value = false
    error.value = null
    currentPage.value = 1
    pageSize.value = 20
    total.value = 0
    filters.value = {}
  }

  return {
    // State
    records,
    filteredRecords,
    currentRecord,
    seasons,
    factories,
    keyModelTypes,
    loading,
    error,
    currentPage,
    pageSize,
    total,
    filters,

    // Computed
    hasRecords,
    hasFilteredRecords,
    isLoading,
    hasError,

    // Actions
    setLoading,
    setError,
    clearError,
    fetchRecords,
    fetchPaginatedRecords,
    fetchRecordById,
    createRecord,
    updateRecord,
    deleteRecord,
    fetchSeasons,
    fetchFactories,
    fetchKeyModelTypes,
    // fetchAllLookupData,
    getDistinctValues,
    setFilters,
    clearFilters,
    applyFilters,
    setFilteredRecords,
    setPage,
    setPageSize,
    nextPage,
    prevPage,
    resetStore
  }
})

