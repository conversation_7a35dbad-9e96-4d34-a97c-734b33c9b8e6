<template>
  <el-card class="filters-card" shadow="never">
    <template #header>
      <div class="filters-header">
        <h3>Filters</h3>
        <div class="filters-actions">
          <el-button
            v-if="hasActiveFilters"
            type="text"
            @click="handleClearAll"
            size="small"
          >
            Clear All
          </el-button>
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="loading"
            size="small"
          >
            Search
          </el-button>
        </div>
      </div>
    </template>

    <el-form
      :model="filters"
      label-position="top"
      class="filters-form filters-row"
    >
      <el-form-item label="Colorway ID">
        <el-select-v2
          v-model="filters.colorwayId"
          :options="colorwayOptions"
          clearable
          placeholder="Enter Colorway ID"
          @keyup.enter="handleSearch"
          :loading="loadingDistinctValues"
          filterable
        />
      </el-form-item>

      <el-form-item label="Material ID">
        <el-select-v2
          v-model="filters.materialId"
          :options="materialOptions"
          clearable
          placeholder="Enter Material ID"
          @keyup.enter="handleSearch"
          :loading="loadingDistinctValues"
          filterable
        />
      </el-form-item>

      <el-form-item label="Key Model Type">
        <el-select
          v-model="filters.keyModelType"
          clearable
          placeholder="Select Key Model Type"
        >
          <el-option label="HR material" value="HR material" />
          <el-option label="Express calendar" value="Express calendar" />
          <el-option label="Incubate models" value="Incubate models" />
          <el-option label="Hybrid & Next" value="Hybrid & Next" />
          <el-option
            label="Design/ Construction complexity"
            value="Design/ Construction complexity"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Product Name">
        <el-input
          v-model="filters.productName"
          clearable
          placeholder="Enter Product Name"
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item label="ISD">
        <el-input
          v-model="filters.isd"
          clearable
          placeholder="Enter ISD"
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item label="Factory">
        <el-select-v2
          v-model="filters.fty"
          :options="factoryOptions"
          clearable
          placeholder="Enter Factory"
          @keyup.enter="handleSearch"
          :loading="loadingDistinctValues"
          filterable
        />
      </el-form-item>

      <el-form-item label="Season">
        <el-select-v2
          v-model="filters.season"
          :options="seasonOptions"
          clearable
          placeholder="Enter Season"
          @keyup.enter="handleSearch"
          :loading="loadingDistinctValues"
          filterable
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useKeyModelFilters } from "@/composables/useKeyModelFilters";
import { useFilter } from "@/composables/useFilter";
import { useKeyModelStore } from "@/stores/keymodel";
import { ElMessage } from "element-plus";

interface Emits {
  (e: "search", filters: any): void;
  (e: "clear"): void;
}

const emit = defineEmits<Emits>();

// Store
const keyModelStore = useKeyModelStore();

// Composables
const {
  filters,
  hasActiveFilters,
  filterSummary,
  clearAllFilters,
  validateFilters,
  getCleanFilters,
} = useKeyModelFilters();

const { distinctValues, loadingDistinctValues, fetchDistinctValues } =
  useFilter();

// Computed
const loading = computed(() => keyModelStore.loading);

const filterSummaryArray = computed(() => {
  if (!filterSummary.value) return [];
  return filterSummary.value.split(", ");
});

// Options for el-select-v2
const colorwayOptions = computed(() =>
  distinctValues.value.colorways.map((item) => ({ label: item, value: item }))
);

const materialOptions = computed(() =>
  distinctValues.value.materials.map((item) => ({ label: item, value: item }))
);

const factoryOptions = computed(() =>
  distinctValues.value.factories.map((item) => ({ label: item, value: item }))
);

const seasonOptions = computed(() =>
  distinctValues.value.seasons.map((item) => ({ label: item, value: item }))
);

// Remote query functions for el-select-v2
const queryColorways = (query: string) => {
  if (query) {
    return distinctValues.value.colorways
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.colorways.map((item) => ({
    label: item,
    value: item,
  }));
};

const queryMaterials = (query: string) => {
  if (query) {
    return distinctValues.value.materials
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.materials.map((item) => ({
    label: item,
    value: item,
  }));
};

const queryFactories = (query: string) => {
  if (query) {
    return distinctValues.value.factories
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.factories.map((item) => ({
    label: item,
    value: item,
  }));
};

const querySeasons = (query: string) => {
  if (query) {
    return distinctValues.value.seasons
      .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
      .map((item) => ({ label: item, value: item }));
  }
  return distinctValues.value.seasons.map((item) => ({
    label: item,
    value: item,
  }));
};

// Methods
const handleSearch = async () => {
  const validation = validateFilters();

  if (!validation.isValid) {
    ElMessage.error(validation.errors.join("\n"));
    return;
  }

  const cleanFilters = getCleanFilters();
  emit("search", cleanFilters);
};

const handleClearAll = () => {
  clearAllFilters();
  emit("clear");
};

const clearFilterBySummary = (filterText: string) => {
  // Parse the filter text to determine which filter to clear
  if (filterText.includes("Colorway ID:")) {
    filters.value.colorwayId = "";
  } else if (filterText.includes("Material ID:")) {
    filters.value.materialId = "";
  } else if (filterText.includes("Key Model Type:")) {
    filters.value.keyModelType = "";
  } else if (filterText.includes("Product Name:")) {
    filters.value.productName = "";
  } else if (filterText.includes("ISD:")) {
    filters.value.isd = "";
  } else if (filterText.includes("Factory:")) {
    filters.value.fty = "";
  } else if (filterText.includes("Season:")) {
    filters.value.season = "";
  } else if (filterText.includes("Status:")) {
    filters.value.isActive = undefined;
  }
};

// Lifecycle
onMounted(async () => {
  // Fetch initial distinct values
  await fetchDistinctValues();
});
</script>

<style scoped>
.filters-card {
  margin-bottom: 24px;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filters-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.filters-actions {
  display: flex;
  gap: 8px;
}

.filters-form {
  padding: 0;
}

.filter-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.filter-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}
.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.filters-row .el-form-item {
  margin-bottom: 0; /* bỏ margin dưới mặc định */
}
</style>
