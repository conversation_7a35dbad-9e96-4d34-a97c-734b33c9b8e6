<template>
  <el-header class="header">
    <div class="header-left">
      <el-button text @click="toggleSidebar" class="sidebar-toggle">
        <el-icon size="20">
          <Fold v-if="!sidebarCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/key-model-tracking' }">{{
          "dashboard"
        }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="header-right">
      <el-button text @click="toggleDarkMode" class="theme-toggle">
        <el-icon size="20">
          <Moon v-if="!isDarkMode" />
          <Sunny v-else />
        </el-icon>
      </el-button>

      <el-dropdown @command="handleUserCommand" trigger="click">
        <span class="user-dropdown">
          <el-avatar :size="32" icon="UserFilled" />
          <span class="username">{{ user?.userName || t("mes.user") }}</span>
          <el-icon>
            <ArrowDown />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon>
                <User />
              </el-icon>
              Profile
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon>
                <Setting />
              </el-icon>
              Settings
            </el-dropdown-item>
            <el-dropdown-item command="logout" divided>
              <el-icon>
                <SwitchButton />
              </el-icon>
              Logout
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app";
import { useAuthStore } from "@/stores/auth";
import { ElMessageBox, ElMessage } from "element-plus";
import LanguageSwitcher from "@/components/common/LanguageSwitcher.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const appStore = useAppStore();
const authStore = useAuthStore();

// Initialize auth when component mounts
onMounted(() => {
  authStore.initializeAuth();
});

const user = computed(() => authStore.user);

const isDarkMode = computed(() => appStore.isDarkMode);
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed);

const pageTitle = computed(() => {
  const i18nKey = (route.meta.title as string) || "dashboard";
  return t(i18nKey);
});

const toggleSidebar = () => {
  appStore.toggleSidebar();
};

const toggleDarkMode = () => {
  appStore.toggleDarkMode();
};

const handleUserCommand = async (command: string) => {
  if (command === "logout") {
    try {
      await ElMessageBox.confirm(
        "Bạn có chắc chắn muốn đăng xuất?",
        "Xác nhận đăng xuất",
        {
          confirmButtonText: "Đăng xuất",
          cancelButtonText: "Hủy",
          type: "warning",
        }
      );

      // Thực hiện logout
      authStore.logout();

      // Hiển thị thông báo thành công
      ElMessage.success("Đăng xuất thành công!");

      // Chuyển hướng về trang login
      await router.push("/login");
    } catch (error) {
      // User cancelled logout
      console.log("Logout cancelled");
    }
  }
};
</script>

<style scoped>
.header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color);
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  color: var(--el-text-color-primary);
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-dropdown:hover {
  background-color: var(--el-fill-color-light);
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.theme-toggle {
  color: var(--el-text-color-primary);
}
</style>
