<template>
  <div class="not-found-page">
    <el-result title="404" sub-title="Xin lỗi, trang không tồn tại">
      <template #icon>
        <el-icon size="120" color="#409eff">
          <Search />
        </el-icon>
      </template>
      <template #extra>
        <el-button type="primary" @click="goHome">
          Về trang chủ
        </el-button>
        <el-button @click="goBack">
          Quay lại
        </el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = (): void => {
  router.push('/key-model-tracking')
}

const goBack = (): void => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

:deep(.el-result__extra) {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>