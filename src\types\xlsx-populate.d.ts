declare module 'xlsx-populate' {
  interface Cell {
    value(value?: any): any
    style(style?: any): any
    address(): string
  }

  interface Sheet {
    cell(address: string): Cell
    usedRange(): Cell[]
  }

  interface Workbook {
    sheet(index: number): Sheet
    addSheet(name: string): Sheet
    outputAsync(options?: { type?: 'blob' | 'arraybuffer' | 'base64' }): Promise<Blob | ArrayBuffer | string>
  }

  interface XlsxPopulate {
    fromDataAsync(data: ArrayBuffer): Promise<Workbook>
  }

  const XlsxPopulate: XlsxPopulate
  export default XlsxPopulate
} 