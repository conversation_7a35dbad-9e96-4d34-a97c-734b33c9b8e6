import axios from 'axios'
import { ElMessage } from 'element-plus'

const urlIp = `${(import.meta as any).env.VITE_BACKEND_URL}api`;
const api = axios.create({
  baseURL: urlIp,
  timeout: 100000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.message || error.message || 'Có lỗi xảy ra'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default api 