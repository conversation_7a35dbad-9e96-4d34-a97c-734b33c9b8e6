<template>
  <!-- Skeleton Loading -->
  <div v-if="loading" class="skeleton-container">
    <el-skeleton :rows="10" animated />
  </div>

  <!-- Data Table -->
  <el-table
    v-else
    :data="records"
    style="margin-bottom: 24px"
    @row-click="handleRowClick"
    class="keymodel-data-table"
    :empty-text="emptyText"
  >
    <el-table-column type="index" label="#" width="50" />
    <el-table-column label="Colorway ID" prop="colorwayId" width="120" />
    <el-table-column label="Material ID" prop="materialId" width="120" />
    <el-table-column label="Key Model Type" prop="keyModelType" width="140" />
    <el-table-column label="Product Name" prop="productName" min-width="200" />
    <el-table-column label="FTY" prop="fty" width="100" />
    <el-table-column label="Quantity" prop="quantity" width="100">
      <template #default="{ row }">
        <span>{{ row.quantity || "N/A" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="ISD" prop="isd" width="120" />
    <el-table-column label="Season" prop="season" width="100" />
    <el-table-column label="PO Received Date" width="140">
      <template #default="{ row }">
        <span>{{ formatDate(row.poReceivedDate) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="userId" label="User ID" width="120" />

    <el-table-column label="Action" width="220" fixed="right" class="action">
      <template #default="{ row, $index }">
        <div class="action-buttons">
          <el-button
            size="small"
            type="success"
            @click.stop="handleCreateProductDialog(row, $index)"
          >
            PRODUCT CREATION
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click.stop="handleCommercialStageDialog(row, $index)"
          >
            COMMERCIAL STAGE
          </el-button>
          <el-button
            size="small"
            type="info"
            @click.stop="handleManufacturingStageDialog(row, $index)"
          >
            MANUFACTURING STAGE
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click.stop="handleEdit(row, $index)"
          >
            View All
          </el-button>
        </div>

        <!-- <el-button
          size="small"
          type="success"
          @click.stop="handleExportSingle(row)"
        >
          Export
        </el-button> -->
        <!-- <el-button
          size="small"
          type="danger"
          @click.stop="handleDelete(row, $index)"
        >
          Delete
        </el-button> -->
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { KeyModelRecord } from "@/api/keymodel";

interface Props {
  records: KeyModelRecord[];
  loading?: boolean;
  showStatus?: boolean;
  hasActiveSearch?: boolean;
}

interface Emits {
  (e: "row-click", record: KeyModelRecord): void;
  (e: "edit", record: KeyModelRecord, index: number): void;
  (e: "export-single", record: KeyModelRecord): void;
  (e: "delete", record: KeyModelRecord, index: number): void;
  (e: "create-product-dialog", record: KeyModelRecord, index: number): void;
  (e: "commercial-stage-dialog", record: KeyModelRecord, index: number): void;
  (
    e: "manufacturing-stage-dialog",
    record: KeyModelRecord,
    index: number,
  ): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showStatus: false,
  hasActiveSearch: false,
});

const emit = defineEmits<Emits>();

// Computed
const emptyText = computed(() => {
  if (props.hasActiveSearch) {
    return "No data found matching your search criteria";
  }
  return "No data available";
});

// Event handlers
const handleRowClick = (record: KeyModelRecord) => {
  emit("row-click", record);
};

const handleEdit = (record: KeyModelRecord, index: number) => {
  emit("edit", record, index);
};

const handleCreateProductDialog = (record: KeyModelRecord, index: number) => {
  emit("create-product-dialog", record, index);
};

const handleCommercialStageDialog = (record: KeyModelRecord, index: number) => {
  emit("commercial-stage-dialog", record, index);
};

const handleManufacturingStageDialog = (
  record: KeyModelRecord,
  index: number,
) => {
  emit("manufacturing-stage-dialog", record, index);
};

const handleExportSingle = (record: KeyModelRecord) => {
  emit("export-single", record);
};

const handleDelete = (record: KeyModelRecord, index: number) => {
  emit("delete", record, index);
};

// Utility functions
const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("vi-VN");
};
</script>

<style scoped>
.keymodel-data-table {
  width: 100%;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
:deep.el-button + .el-button {
  margin-left: 12px;
}
:deep(.el-button + .el-button) {
  margin-left: 0;
  margin-top: 2px;
}
/* .skeleton-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
} */

:deep(.el-skeleton__item) {
  margin-bottom: 16px;
}

:deep(.el-skeleton__paragraph) {
  margin-bottom: 8px;
}
</style>
