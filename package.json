{"name": "ICANDOIT", "version": "0.0.0", "scripts": {"lint": "eslint \"src/**/*.{js,vue}\"", "dev": "cross-env NODE_ENV=development vite --host", "serve": "cross-env NODE_ENV=development vite --host", "build": "cross-env NODE_ENV=production vite build", "preview": "cross-env vite preview", "build:preview": "vite build --mode production && vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "echarts": "^5.6.0", "element-plus": "^2.10.2", "exceljs": "^4.4.0", "flagpack": "^1.0.5", "pinia": "^2.3.1", "vue": "^3.4.0", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1", "xlsx-populate": "^1.21.0"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.0.4", "prettier": "^3.3.3", "typescript": "^5.3.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.5"}}