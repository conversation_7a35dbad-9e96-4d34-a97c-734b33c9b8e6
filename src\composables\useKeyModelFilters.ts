import { ref, computed, watch } from 'vue'
import type { KeyModelFilterOptions } from '@/api/keymodel'

export function useKeyModelFilters() {
  // Filter state
  const filters = ref<KeyModelFilterOptions>({
    colorwayId: '',
    materialId: '',
    keyModelType: '',
    productName: '',
    isd: '',
    fty: '',
    season: '',
    isActive: undefined
  })

  // Computed properties
  const hasActiveFilters = computed(() => {
    return Object.entries(filters.value).some(([key, value]) => {
      if (key === 'isActive') {
        return value !== undefined
      }
      return value && value.toString().trim() !== ''
    })
  })

  const activeFilterCount = computed(() => {
    return Object.entries(filters.value).filter(([key, value]) => {
      if (key === 'isActive') {
        return value !== undefined
      }
      return value && value.toString().trim() !== ''
    }).length
  })

  const filterSummary = computed(() => {
    const activeFilters: string[] = []
    
    if (filters.value.colorwayId) {
      activeFilters.push(`Colorway ID: ${filters.value.colorwayId}`)
    }
    if (filters.value.materialId) {
      activeFilters.push(`Material ID: ${filters.value.materialId}`)
    }
    if (filters.value.keyModelType) {
      activeFilters.push(`Key Model Type: ${filters.value.keyModelType}`)
    }
    if (filters.value.productName) {
      activeFilters.push(`Product Name: ${filters.value.productName}`)
    }
    if (filters.value.isd) {
      activeFilters.push(`ISD: ${filters.value.isd}`)
    }
    if (filters.value.fty) {
      activeFilters.push(`Factory: ${filters.value.fty}`)
    }
    if (filters.value.season) {
      activeFilters.push(`Season: ${filters.value.season}`)
    }
    if (filters.value.isActive !== undefined) {
      activeFilters.push(`Status: ${filters.value.isActive ? 'Active' : 'Inactive'}`)
    }

    return activeFilters.join(', ')
  })

  // Methods
  const setFilter = (key: keyof KeyModelFilterOptions, value: any): void => {
    filters.value[key] = value
  }

  const setKeyModelType = (keyModelType: string): void => {
    filters.value.keyModelType = keyModelType
  }

  const setFactory = (fty: string): void => {
    filters.value.fty = fty
  }

  const setSeason = (season: string): void => {
    filters.value.season = season
  }

  const setStatus = (isActive: boolean | undefined): void => {
    filters.value.isActive = isActive
  }

  const clearFilter = (key: keyof KeyModelFilterOptions): void => {
    if (key === 'isActive') {
      filters.value[key] = undefined
    } else {
      (filters.value as any)[key] = ''
    }
  }

  const clearAllFilters = (): void => {
    filters.value = {
      colorwayId: '',
      materialId: '',
      keyModelType: '',
      productName: '',
      isd: '',
      fty: '',
      season: '',
      isActive: undefined
    }
  }

  const getFilterSummary = (): string => {
    return filterSummary.value
  }

  const validateFilters = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (filters.value.colorwayId && filters.value.colorwayId.length < 2) {
      errors.push('Colorway ID phải có ít nhất 2 ký tự.')
    }
    
    if (filters.value.materialId && filters.value.materialId.length < 2) {
      errors.push('Material ID phải có ít nhất 2 ký tự.')
    }
    
    if (filters.value.productName && filters.value.productName.length < 2) {
      errors.push('Product Name phải có ít nhất 2 ký tự.')
    }
    
    if (filters.value.isd && filters.value.isd.length < 2) {
      errors.push('ISD phải có ít nhất 2 ký tự.')
    }
    
    if (filters.value.fty && filters.value.fty.length < 2) {
      errors.push('Factory phải có ít nhất 2 ký tự.')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Get clean filters (remove empty values)
  const getCleanFilters = (): KeyModelFilterOptions => {
    const cleanFilters: KeyModelFilterOptions = {}
    
    Object.entries(filters.value).forEach(([key, value]) => {
      if (key === 'isActive') {
        if (value !== undefined) {
          (cleanFilters as any)[key] = value
        }
      } else if (value && value.toString().trim() !== '') {
        (cleanFilters as any)[key] = value
      }
    })
    
    return cleanFilters
  }

  // Check if there are any active filters
  const hasAnyActiveFilters = (): boolean => {
    return hasActiveFilters.value
  }

  // Get active filter count
  const getActiveFilterCount = (): number => {
    return activeFilterCount.value
  }

  return {
    // State
    filters,
    
    // Computed
    hasActiveFilters,
    activeFilterCount,
    filterSummary,
    
    // Methods
    setFilter,
    setKeyModelType,
    setFactory,
    setSeason,
    setStatus,
    clearFilter,
    clearAllFilters,
    getFilterSummary,
    validateFilters,
    getCleanFilters,
    hasAnyActiveFilters,
    getActiveFilterCount
  }
} 