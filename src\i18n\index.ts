import { createI18n } from 'vue-i18n'
import vi from '@/locales/vi'
import zh from '@/locales/zh'
import en from '@/locales/en'

const messages = {
  vi,
  zh,
  en
}

// Language detection and persistence
const getDefaultLocale = (): string => {
  // Check localStorage first
  const savedLocale = localStorage.getItem('locale')
  if (savedLocale) {
    return savedLocale
  }
  
  // Check browser language
  const browserLang = navigator.language.toLowerCase()
  if (browserLang.startsWith('vi')) {
    return 'vi'
  } else if (browserLang.startsWith('zh')) {
    return 'zh'
  } else if (browserLang.startsWith('en')) {
    return 'en'
  }
  
  // Default to Vietnamese
  return 'vi'
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'vi',
  messages,
  globalInjection: true, // Enable global injection
  silentTranslationWarn: true, // Suppress warnings for missing translations
  silentFallbackWarn: true // Suppress warnings for fallback translations
})

// Persist locale changes
const originalSetLocaleMessage = i18n.global.setLocaleMessage
i18n.global.setLocaleMessage = function(locale: string, message: any) {
  originalSetLocaleMessage.call(this, locale, message)
  localStorage.setItem('locale', locale)
}

export default i18n 