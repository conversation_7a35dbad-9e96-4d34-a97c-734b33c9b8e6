import { ref, computed } from 'vue'
import type { KeyModelRecord } from '@/api/keymodel'

export function useKeyModelPagination() {
  // Pagination state
  const currentPage = ref(1)
  const pageSize = ref(10) // Changed from 20 to 10 for testing
  const totalRecords = ref<KeyModelRecord[]>([])

  // Computed properties
  const totalPages = computed(() => {
    return Math.ceil(totalRecords.value.length / pageSize.value)
  })

  const hasNextPage = computed(() => {
    return currentPage.value < totalPages.value
  })

  const hasPrevPage = computed(() => {
    return currentPage.value > 1
  })

  const startIndex = computed(() => {
    return (currentPage.value - 1) * pageSize.value
  })

  const endIndex = computed(() => {
    return Math.min(startIndex.value + pageSize.value, totalRecords.value.length)
  })

  const paginatedRecords = computed(() => {
    return totalRecords.value.slice(startIndex.value, endIndex.value)
  })

  const pageInfo = computed(() => {
    const total = totalRecords.value.length
    const start = startIndex.value + 1
    const end = endIndex.value
    
    return {
      total,
      start,
      end,
      currentPage: currentPage.value,
      totalPages: totalPages.value,
      pageSize: pageSize.value
    }
  })

  // Methods
  const setRecords = (records: KeyModelRecord[]) => {
    totalRecords.value = records
    // Reset to first page when records change
    currentPage.value = 1
  }

  const setPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    // Reset to first page when page size changes
    currentPage.value = 1
  }

  const nextPage = () => {
    if (hasNextPage.value) {
      currentPage.value++
    }
  }

  const prevPage = () => {
    if (hasPrevPage.value) {
      currentPage.value--
    }
  }

  const goToFirstPage = () => {
    currentPage.value = 1
  }

  const goToLastPage = () => {
    currentPage.value = totalPages.value
  }

  const getPageNumbers = (maxVisible: number = 5) => {
    const pages: (number | string)[] = []
    const total = totalPages.value
    const current = currentPage.value

    if (total <= maxVisible) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      if (current > 3) {
        pages.push('...')
      }

      // Show pages around current page
      const start = Math.max(2, current - 1)
      const end = Math.min(total - 1, current + 1)

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== total) {
          pages.push(i)
        }
      }

      if (current < total - 2) {
        pages.push('...')
      }

      // Always show last page
      if (total > 1) {
        pages.push(total)
      }
    }

    return pages
  }

  const resetPagination = () => {
    currentPage.value = 1
    totalRecords.value = []
  }

  return {
    // State
    currentPage,
    pageSize,
    totalRecords,

    // Computed
    totalPages,
    hasNextPage,
    hasPrevPage,
    paginatedRecords,
    pageInfo,

    // Methods
    setRecords,
    setPage,
    setPageSize,
    nextPage,
    prevPage,
    goToFirstPage,
    goToLastPage,
    getPageNumbers,
    resetPagination
  }
} 