// KFXXZL Distinct Value Filter Types
// Based on the KFXXZL Distinct APIs documentation

// Base API Response Structure
export interface KFXXZLDistinctResponse<T = any> {
  code: number
  message: string
  data: T
}

// Distinct Value Types
export interface Colorway {
  colorwayId: string
}

export interface Material {
  materialId: string
}

export interface Season {
  season: string
}

export interface Factory {
  factory: string
}

// Filter Options for KFXXZL Data
export interface KFXXZLFilterOptions {
  // Colorway filters
  colorwayId?: string
  colorwayIds?: string[]
  
  // Material filters
  materialId?: string
  materialIds?: string[]
  
  // Season filters
  season?: string
  seasons?: string[]
  
  // Factory filters
  factory?: string
  factories?: string[]
  
  // Search and pagination
  search?: string
  pageNumber?: number
  pageSize?: number
  
  // Date range filters (if applicable)
  dateFrom?: string
  dateTo?: string
  
  // Status filters
  isActive?: boolean
}

// Advanced Filter Options with operators
export interface KFXXZLAdvancedFilter {
  field: 'colorwayId' | 'materialId' | 'season' | 'factory'
  operator: 'eq' | 'ne' | 'in' | 'notIn' | 'like' | 'startsWith' | 'endsWith'
  value: string | string[]
}

export interface KFXXZLAdvancedFilterOptions {
  filters: KFXXZLAdvancedFilter[]
  search?: string
  pageNumber?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Filter State for UI Components
export interface KFXXZLFilterState {
  // Basic filters
  colorwayId: string
  materialId: string
  season: string
  factory: string
  
  // Advanced filters
  advancedFilters: KFXXZLAdvancedFilter[]
  
  // Search
  search: string
  
  // Pagination
  pageNumber: number
  pageSize: number
  
  // Sorting
  sortBy: string
  sortOrder: 'asc' | 'desc'
  
  // UI state
  isAdvancedMode: boolean
  isExpanded: boolean
}

// Filter Validation
export interface KFXXZLFilterValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Filter Summary for display
export interface KFXXZLFilterSummary {
  activeFilters: string[]
  totalActiveFilters: number
  hasActiveFilters: boolean
  filterDescription: string
}

// API Endpoint Types
export type KFXXZLEndpoint = 
  | '/api/colorways'
  | '/api/materials'
  | '/api/seasons'
  | '/api/factories'

// Filter Field Mapping
export const KFXXZL_FIELD_MAPPING = {
  colorwayId: 'DEVCODE',
  materialId: 'ARTICLE',
  season: 'jijie',
  factory: 'kfcq'
} as const

// Filter Field Types
export type KFXXZLField = keyof typeof KFXXZL_FIELD_MAPPING

// Filter Operators
export const KFXXZL_OPERATORS = {
  eq: 'equals',
  ne: 'not equals',
  in: 'in list',
  notIn: 'not in list',
  like: 'contains',
  startsWith: 'starts with',
  endsWith: 'ends with'
} as const

export type KFXXZLOperator = keyof typeof KFXXZL_OPERATORS

// Default Filter Values
export const DEFAULT_KFXXZL_FILTERS: KFXXZLFilterState = {
  colorwayId: '',
  materialId: '',
  season: '',
  factory: '',
  advancedFilters: [],
  search: '',
  pageNumber: 1,
  pageSize: 20,
  sortBy: 'colorwayId',
  sortOrder: 'asc',
  isAdvancedMode: false,
  isExpanded: false
}

// Filter Presets
export interface KFXXZLFilterPreset {
  id: string
  name: string
  description?: string
  filters: KFXXZLFilterOptions
  isDefault?: boolean
  createdAt?: string
  updatedAt?: string
}

// All types are exported above
