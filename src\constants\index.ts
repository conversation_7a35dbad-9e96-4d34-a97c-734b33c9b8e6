// API Constants
export const API_ENDPOINTS = {
  AUTH: '/auth',
  ORDERS: '/orders',
  PRODUCTS: '/products',
  MATERIALS: '/materials',
  BOM: '/bom',
  MOVEMENTS: '/movements',
  INVENTORY: '/inventory',
  SHIPMENT: '/api/v1/deckers/report'
} as const

export const API_STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const

// Application Constants
export const APP_CONFIG = {
  NAME: 'ICANDOIT ERP',
  VERSION: '1.0.0',
  DEFAULT_LOCALE: 'vi',
  SUPPORTED_LOCALES: ['en', 'vi', 'zh'],
  DEFAULT_THEME: 'light',
  API_TIMEOUT: 10000,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
  }
} as const

// Order Constants
export const ORDER_STATUS = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
} as const

export const ORDER_STATUS_LABELS = {
  [ORDER_STATUS.PENDING]: 'mes.pending',
  [ORDER_STATUS.IN_PROGRESS]: 'mes.inProgress',
  [ORDER_STATUS.COMPLETED]: 'mes.completed',
  [ORDER_STATUS.CANCELLED]: 'mes.cancelled'
} as const

// Product Constants
export const PRODUCT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  DISCONTINUED: 'DISCONTINUED'
} as const

export const PRODUCT_STATUS_LABELS = {
  [PRODUCT_STATUS.ACTIVE]: 'mes.active',
  [PRODUCT_STATUS.INACTIVE]: 'mes.inactive',
  [PRODUCT_STATUS.DISCONTINUED]: 'mes.discontinued'
} as const

// Movement Constants
export const MOVEMENT_TYPE = {
  STOCK_IN: 'STOCK_IN',
  STOCK_OUT: 'STOCK_OUT',
  STOCK_ADJUST: 'STOCK_ADJUST'
} as const

export const MOVEMENT_TYPE_LABELS = {
  [MOVEMENT_TYPE.STOCK_IN]: 'mes.stockIn',
  [MOVEMENT_TYPE.STOCK_OUT]: 'mes.stockOut',
  [MOVEMENT_TYPE.STOCK_ADJUST]: 'mes.stockAdjust'
} as const

// Validation Constants
export const VALIDATION_RULES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  PATTERN: 'pattern',
  MIN_VALUE: 'minValue',
  MAX_VALUE: 'maxValue'
} as const

export const VALIDATION_MESSAGES = {
  REQUIRED: 'mes.fieldRequired',
  EMAIL: 'mes.invalidEmail',
  MIN_LENGTH: 'mes.minLength',
  MAX_LENGTH: 'mes.maxLength',
  PATTERN: 'mes.invalidPattern',
  MIN_VALUE: 'mes.minValue',
  MAX_VALUE: 'mes.maxValue'
} as const

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  DISPLAY_TIME: 'DD/MM/YYYY HH:mm',
  API: 'YYYY-MM-DDTHH:mm:ssZ',
  API_DATE: 'YYYY-MM-DD'
} as const

// Local Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER: 'user',
  THEME: 'theme',
  LOCALE: 'locale',
  SIDEBAR_COLLAPSED: 'sidebarCollapsed'
} as const

// Route Names
export const ROUTE_NAMES = {
  LOGIN: 'Login',
  DASHBOARD: 'Dashboard',
  PRODUCTS: 'Products',
  ORDERS: 'Orders',
  MATERIALS: 'Materials',
  BOM: 'BOM',
  MOVEMENTS: 'Movements',
  NOT_FOUND: 'NotFound'
} as const

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'mes.networkError',
  UNAUTHORIZED: 'mes.unauthorizedError',
  NOT_FOUND: 'mes.notFoundError',
  VALIDATION_ERROR: 'mes.validationError',
  GENERAL_ERROR: 'mes.generalError',
  API_ERROR: 'mes.apiError'
} as const

// Success Messages
export const SUCCESS_MESSAGES = {
  CREATE: 'mes.createSuccess',
  UPDATE: 'mes.updateSuccess',
  DELETE: 'mes.deleteSuccess',
  SAVE: 'mes.saveSuccess'
} as const 