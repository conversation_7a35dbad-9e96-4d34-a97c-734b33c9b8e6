import { ref, computed, watch, onMounted } from 'vue'
import type { 
  KFXXZLFilterState, 
  KFXXZLFilterOptions, 
  KFXXZLAdvancedFilter, 
  KFXXZLAdvancedFilterOptions,
  KFXXZLFilterValidation,
  KFXXZLFilterSummary
} from '@/types/filter'
import { DEFAULT_KFXXZL_FILTERS } from '@/types/filter'
import { 
  getDistinctColorways,
  getDistinctMaterials,
  getDistinctSeasons,
  getDistinctFactories,
  getAllDistinctValues,
  getDistinctValuesByField,
  searchDistinctValues,
  getDistinctValuesWithAdvancedFilters,
  getCachedDistinctValues,
  clearDistinctValuesCache
} from '@/api/filter'

export function useFilter() {
  // Filter state
  const filters = ref<KFXXZLFilterState>({
    colorwayId: '',
    materialId: '',
    season: '',
    factory: '',
    advancedFilters: [],
    search: '',
    pageNumber: 1,
    pageSize: 20,
    sortBy: 'colorwayId',
    sortOrder: 'asc',
    isAdvancedMode: false,
    isExpanded: false
  })

  // Distinct values state
  const distinctValues = ref<{
    colorways: string[]
    materials: string[]
    seasons: string[]
    factories: string[]
  }>({
    colorways: [],
    materials: [],
    seasons: [],
    factories: []
  })

  // Loading states
  const loading = ref(false)
  const loadingDistinctValues = ref(false)

  // Error state
  const error = ref<string | null>(null)

  // Computed properties
  const hasActiveFilters = computed(() => {
    return !!(
      filters.value.colorwayId ||
      filters.value.materialId ||
      filters.value.season ||
      filters.value.factory ||
      filters.value.search ||
      filters.value.advancedFilters.length > 0
    )
  })

  const activeFilterCount = computed(() => {
    let count = 0
    if (filters.value.colorwayId) count++
    if (filters.value.materialId) count++
    if (filters.value.season) count++
    if (filters.value.factory) count++
    if (filters.value.search) count++
    count += filters.value.advancedFilters.length
    return count
  })

  const filterSummary = computed((): KFXXZLFilterSummary => {
    const activeFilters: string[] = []
    
    if (filters.value.colorwayId) {
      activeFilters.push(`Colorway ID: ${filters.value.colorwayId}`)
    }
    if (filters.value.materialId) {
      activeFilters.push(`Material ID: ${filters.value.materialId}`)
    }
    if (filters.value.season) {
      activeFilters.push(`Season: ${filters.value.season}`)
    }
    if (filters.value.factory) {
      activeFilters.push(`Factory: ${filters.value.factory}`)
    }
    if (filters.value.search) {
      activeFilters.push(`Search: ${filters.value.search}`)
    }
    
    filters.value.advancedFilters.forEach((filter, index) => {
      const operatorText = getOperatorText(filter.operator)
      const valueText = Array.isArray(filter.value) ? filter.value.join(', ') : filter.value
      activeFilters.push(`Advanced ${index + 1}: ${filter.field} ${operatorText} ${valueText}`)
    })

    return {
      activeFilters,
      totalActiveFilters: activeFilters.length,
      hasActiveFilters: activeFilters.length > 0,
      filterDescription: activeFilters.join(', ')
    }
  })

  const hasAdvancedFilters = computed(() => {
    return filters.value.advancedFilters.length > 0
  })

  const isFilterExpanded = computed(() => {
    return filters.value.isExpanded
  })

  // Methods
  const setFilter = (key: keyof KFXXZLFilterState, value: any): void => {
    if (key in filters.value) {
      (filters.value as any)[key] = value
    }
  }

  const setBasicFilter = (field: 'colorwayId' | 'materialId' | 'season' | 'factory', value: string): void => {
    filters.value[field] = value
  }

  const setSearch = (search: string): void => {
    filters.value.search = search
  }

  const setPagination = (pageNumber: number, pageSize: number): void => {
    filters.value.pageNumber = pageNumber
    filters.value.pageSize = pageSize
  }

  const setSorting = (sortBy: string, sortOrder: 'asc' | 'desc'): void => {
    filters.value.sortBy = sortBy
    filters.value.sortOrder = sortOrder
  }

  const toggleAdvancedMode = (): void => {
    filters.value.isAdvancedMode = !filters.value.isAdvancedMode
  }

  const toggleExpanded = (): void => {
    filters.value.isExpanded = !filters.value.isExpanded
  }

  const addAdvancedFilter = (filter: KFXXZLAdvancedFilter): void => {
    filters.value.advancedFilters.push(filter)
  }

  const updateAdvancedFilter = (index: number, filter: KFXXZLAdvancedFilter): void => {
    if (index >= 0 && index < filters.value.advancedFilters.length) {
      filters.value.advancedFilters[index] = filter
    }
  }

  const removeAdvancedFilter = (index: number): void => {
    if (index >= 0 && index < filters.value.advancedFilters.length) {
      filters.value.advancedFilters.splice(index, 1)
    }
  }

  const clearAdvancedFilters = (): void => {
    filters.value.advancedFilters = []
  }

  const clearFilter = (key: keyof KFXXZLFilterState): void => {
    if (key === 'advancedFilters') {
      filters.value.advancedFilters = []
    } else if (key === 'pageNumber' || key === 'pageSize') {
      (filters.value as any)[key] = key === 'pageNumber' ? 1 : 20
    } else if (key === 'sortBy' || key === 'sortOrder') {
      (filters.value as any)[key] = key === 'sortBy' ? 'colorwayId' : 'asc'
    } else if (key === 'isAdvancedMode') {
      filters.value.isAdvancedMode = false
    } else if (key === 'isExpanded') {
      filters.value.isExpanded = false
    } else {
      (filters.value as any)[key] = ''
    }
  }

  const clearAllFilters = (): void => {
    filters.value = {
      colorwayId: '',
      materialId: '',
      season: '',
      factory: '',
      advancedFilters: [],
      search: '',
      pageNumber: 1,
      pageSize: 20,
      sortBy: 'colorwayId',
      sortOrder: 'asc',
      isAdvancedMode: false,
      isExpanded: false
    }
  }

  const resetToDefaults = (): void => {
    filters.value = { ...DEFAULT_KFXXZL_FILTERS }
  }

  // API Methods
  const fetchDistinctValues = async (): Promise<void> => {
    try {
      loadingDistinctValues.value = true
      error.value = null
      
      const values = await getAllDistinctValues()
      distinctValues.value = values
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch distinct values'
      console.error('Error fetching distinct values:', err)
    } finally {
      loadingDistinctValues.value = false
    }
  }

  const fetchDistinctValuesByField = async (field: 'colorwayId' | 'materialId' | 'season' | 'factory'): Promise<string[]> => {
    try {
      loadingDistinctValues.value = true
      error.value = null
      
      const values = await getDistinctValuesByField(field)
      return values
    } catch (err: any) {
      error.value = err.message || `Failed to fetch distinct ${field} values`
      console.error(`Error fetching distinct ${field} values:`, err)
      return []
    } finally {
      loadingDistinctValues.value = false
    }
  }

  const searchWithFilters = async (): Promise<{
    colorways: string[]
    materials: string[]
    seasons: string[]
    factories: string[]
  }> => {
    try {
      loading.value = true
      error.value = null
      
      const filterOptions: KFXXZLFilterOptions = {
        colorwayId: filters.value.colorwayId || undefined,
        materialId: filters.value.materialId || undefined,
        season: filters.value.season || undefined,
        factory: filters.value.factory || undefined,
        search: filters.value.search || undefined,
        pageNumber: filters.value.pageNumber,
        pageSize: filters.value.pageSize
      }
      
      const results = await searchDistinctValues(filterOptions)
      return results
    } catch (err: any) {
      error.value = err.message || 'Failed to search with filters'
      console.error('Error searching with filters:', err)
      return {
        colorways: [],
        materials: [],
        seasons: [],
        factories: []
      }
    } finally {
      loading.value = false
    }
  }

  const searchWithAdvancedFilters = async (): Promise<{
    colorways: string[]
    materials: string[]
    seasons: string[]
    factories: string[]
  }> => {
    try {
      loading.value = true
      error.value = null
      
      const advancedOptions: KFXXZLAdvancedFilterOptions = {
        filters: filters.value.advancedFilters,
        search: filters.value.search || undefined,
        pageNumber: filters.value.pageNumber,
        pageSize: filters.value.pageSize,
        sortBy: filters.value.sortBy,
        sortOrder: filters.value.sortOrder
      }
      
      const results = await getDistinctValuesWithAdvancedFilters(advancedOptions)
      return results
    } catch (err: any) {
      error.value = err.message || 'Failed to search with advanced filters'
      console.error('Error searching with advanced filters:', err)
      return {
        colorways: [],
        materials: [],
        seasons: [],
        factories: []
      }
    } finally {
      loading.value = false
    }
  }

  const getCachedValues = async (ttl?: number): Promise<void> => {
    try {
      loadingDistinctValues.value = true
      error.value = null
      
      const values = await getCachedDistinctValues(ttl)
      distinctValues.value = values
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch cached values'
      console.error('Error fetching cached values:', err)
    } finally {
      loadingDistinctValues.value = false
    }
  }

  const clearCache = (): void => {
    clearDistinctValuesCache()
  }

  // Validation
  const validateFilters = (): KFXXZLFilterValidation => {
    const errors: string[] = []
    const warnings: string[] = []
    
    // Basic validation
    if (filters.value.colorwayId && filters.value.colorwayId.length < 2) {
      errors.push('Colorway ID must be at least 2 characters long')
    }
    
    if (filters.value.materialId && filters.value.materialId.length < 2) {
      errors.push('Material ID must be at least 2 characters long')
    }
    
    if (filters.value.search && filters.value.search.length < 2) {
      errors.push('Search term must be at least 2 characters long')
    }
    
    // Advanced filter validation
    filters.value.advancedFilters.forEach((filter, index) => {
      if (!filter.field || !filter.operator || !filter.value) {
        errors.push(`Advanced filter ${index + 1} is incomplete`)
      }
      
      if (Array.isArray(filter.value) && filter.value.length === 0) {
        errors.push(`Advanced filter ${index + 1} has empty values`)
      }
    })
    
    // Warnings
    if (filters.value.pageSize > 100) {
      warnings.push('Page size is large, this may impact performance')
    }
    
    if (filters.value.advancedFilters.length > 5) {
      warnings.push('Many advanced filters may slow down the search')
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  const getCleanFilters = (): KFXXZLFilterOptions => {
    const cleanFilters: KFXXZLFilterOptions = {}
    
    if (filters.value.colorwayId) cleanFilters.colorwayId = filters.value.colorwayId
    if (filters.value.materialId) cleanFilters.materialId = filters.value.materialId
    if (filters.value.season) cleanFilters.season = filters.value.season
    if (filters.value.factory) cleanFilters.factory = filters.value.factory
    if (filters.value.search) cleanFilters.search = filters.value.search
    if (filters.value.pageNumber > 1) cleanFilters.pageNumber = filters.value.pageNumber
    if (filters.value.pageSize !== 20) cleanFilters.pageSize = filters.value.pageSize
    
    return cleanFilters
  }

  // Utility functions
  const getOperatorText = (operator: string): string => {
    const operatorMap: Record<string, string> = {
      eq: 'equals',
      ne: 'not equals',
      in: 'in list',
      notIn: 'not in list',
      like: 'contains',
      startsWith: 'starts with',
      endsWith: 'ends with'
    }
    return operatorMap[operator] || operator
  }

  const hasAnyActiveFilters = (): boolean => {
    return hasActiveFilters.value
  }

  const getActiveFilterCount = (): number => {
    return activeFilterCount.value
  }

  const getFilterSummary = (): string => {
    return filterSummary.value.filterDescription
  }

  // Watchers
  watch(filters, (newFilters) => {
    // Reset to first page when filters change
    if (newFilters.pageNumber > 1) {
      filters.value.pageNumber = 1
    }
  }, { deep: true })

  // Lifecycle
  onMounted(() => {
    // Fetch initial distinct values
    fetchDistinctValues()
  })

  return {
    // State
    filters,
    distinctValues,
    loading,
    loadingDistinctValues,
    error,
    
    // Computed
    hasActiveFilters,
    activeFilterCount,
    filterSummary,
    hasAdvancedFilters,
    isFilterExpanded,
    
    // Methods
    setFilter,
    setBasicFilter,
    setSearch,
    setPagination,
    setSorting,
    toggleAdvancedMode,
    toggleExpanded,
    addAdvancedFilter,
    updateAdvancedFilter,
    removeAdvancedFilter,
    clearAdvancedFilters,
    clearFilter,
    clearAllFilters,
    resetToDefaults,
    
    // API Methods
    fetchDistinctValues,
    fetchDistinctValuesByField,
    searchWithFilters,
    searchWithAdvancedFilters,
    getCachedValues,
    clearCache,
    
    // Validation
    validateFilters,
    getCleanFilters,
    
    // Utility
    getOperatorText,
    hasAnyActiveFilters,
    getActiveFilterCount,
    getFilterSummary
  }
}
